﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <Reference Include="BPM.AspNetCore">
      <HintPath>..\..\dlls\BPM.AspNetCore.dll</HintPath>
    </Reference>
    <Reference Include="BPM.Core">
      <HintPath>..\..\dlls\BPM.Core.dll</HintPath>
    </Reference>
    <Reference Include="BPM.Data">
      <HintPath>..\..\dlls\BPM.Data.dll</HintPath>
    </Reference>
    <Reference Include="BPM.Data.Sql">
      <HintPath>..\..\dlls\BPM.Data.Sql.dll</HintPath>
    </Reference>
    <Reference Include="BPM.Datas.Dapper">
      <HintPath>..\..\dlls\BPM.Datas.Dapper.dll</HintPath>
    </Reference>
    <Reference Include="BPM.Datas.EntityFramework">
      <HintPath>..\..\dlls\BPM.Datas.EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="BPM.Datas.EntityFramework.SqlServer">
      <HintPath>..\..\dlls\BPM.Datas.EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="BPM.Ddd.Application">
      <HintPath>..\..\dlls\BPM.Ddd.Application.dll</HintPath>
    </Reference>
    <Reference Include="BPM.Ddd.Application.Contracts">
      <HintPath>..\..\dlls\BPM.Ddd.Application.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="BPM.Ddd.Domain">
      <HintPath>..\..\dlls\BPM.Ddd.Domain.dll</HintPath>
    </Reference>
    <Reference Include="BPM.Events">
      <HintPath>..\..\dlls\BPM.Events.dll</HintPath>
    </Reference>
    <Reference Include="BPM.Mapper">
      <HintPath>..\..\dlls\BPM.Mapper.dll</HintPath>
    </Reference>
    <Reference Include="BPM.Permissions">
      <HintPath>..\..\dlls\BPM.Permissions.dll</HintPath>
    </Reference>
    <Reference Include="BPM.Security">
      <HintPath>..\..\dlls\BPM.Security.dll</HintPath>
    </Reference>
    <Reference Include="BPM.Utils">
      <HintPath>..\..\dlls\BPM.Utils.dll</HintPath>
    </Reference>
    <Reference Include="BPM.Utils.Http">
      <HintPath>..\..\dlls\BPM.Utils.Http.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Domain.Shared\Domain.Shared.csproj" />
    <ProjectReference Include="..\Domain\Domain.csproj" />
    <ProjectReference Include="..\Infrastructure\Infrastructure.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Dtos\order\" />
    <Folder Include="Responses\" />
  </ItemGroup>

</Project>
