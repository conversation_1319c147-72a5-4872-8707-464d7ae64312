﻿using System;

namespace AppService.Dtos.card
{
    /// <summary>
    /// 储值卡Dto
    /// </summary>
    public class cardDto
    {
        /// <summary>
        /// 卡号
        /// </summary>
        public string CODE { get; set; }
        /// <summary>
        /// 卡面号
        /// </summary>
        public string SHORTNAME { get; set; }
        /// <summary>
        /// 创建日期
        /// </summary>
        public DateTime? CREDATE { get; set; }
        /// <summary>
        /// 激活日期
        /// </summary>
        public DateTime? ISSDATE { get; set; }
        /// <summary>
        /// 最后登录日期
        /// </summary>
        public DateTime? LSTDATE { get; set; }
        /// <summary>
        /// 有效期
        /// </summary>
        public DateTime? EXPDATE { get; set; }
        /// <summary>
        /// 是否激活
        /// </summary>
        public bool ISACTIVE { get; set; }
        /// <summary>
        /// 面值
        /// </summary>
        public decimal FACE { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public decimal SAVED { get; set; }
        /// <summary>
        /// 余额
        /// </summary>
        public decimal BALANCE { get; set; }

        /// <summary>
        /// 仓号
        /// </summary>
        public string WAREHOUSE { get; set; }

        public string REFCODE { get; set; }

        /// <summary>
        /// 校验码
        /// </summary>
        public string VFCODE { get; set; }

        public string TYPE { get; set; }



    }

    /// <summary>
    /// 储值卡制卡Dto
    /// </summary>
    public class cardVFDto 
    {
        /// <summary>
        /// 号码
        /// </summary>
        public string REFCODE { get; set; }

        /// <summary>
        /// 校验码
        /// </summary>
        public string VFCODE { get; set; }

        /// <summary>
        /// 创建日期
        /// </summary>
        public DateTime CREDATE { get; set; }

        /// <summary>
        /// 卡号
        /// </summary>
        public string CODE { get; set; }

        /// <summary>
        /// 面值
        /// </summary>
        public decimal? FACE { get; set; }


        /// <summary>
        /// 密码
        /// </summary>
        public string PASSWORD { get; set; }

    }

    /// <summary>
    /// 储值卡面值
    /// </summary>
    public class cardFaceDto
    {
        /// <summary>
        /// 主键
        /// </summary>
        public int UID { get; set; }

        /// <summary>
        /// 门店
        /// </summary>

        public string WAREHOUSE { get; set; }

        /// <summary>
        /// 充值面值
        /// </summary>
        public decimal? AMT { get; set; }

        /// <summary>
        /// 实际面值
        /// </summary>
        public decimal? NET { get; set; }

    }
}
