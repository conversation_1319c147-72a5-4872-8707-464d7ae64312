﻿namespace AppService.Dtos.coupon
{
    /// <summary>
    /// 优惠券基类
    /// </summary>
    public class couponBase
    {
        /// <summary>
        /// 优惠券Id
        /// </summary>
        public string couponId { get; set; }

        /// <summary>
        /// 优惠券券码
        /// </summary>
        public string couponNo { get; set; }

        /// <summary>
        ///优惠券名称
        /// </summary>
        public string name { get; set; }

        /// <summary>
        /// 优惠券面额，单位元，保留到小数点后2位
        /// </summary>
        public string discount { get; set; }

        /// <summary>
        /// 优惠券类型：1-满减券;2-兑换券;3-运费券
        /// </summary>
        public int couponType { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string remark { get; set; }

    }
}
