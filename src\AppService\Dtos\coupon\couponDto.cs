﻿namespace AppService.Dtos.coupon
{
    /// <summary>
    /// 优惠券查询Dto
    /// </summary>
    public class couponQueryListDto : couponBase
    {

        /// <summary>
        /// 实际折扣
        /// </summary>
        public string actualDiscount { get; set; }

        /// <summary>
        /// 是否默认选中
        /// </summary>
        public int @checked { get; set; }
        /// <summary>
        /// 描述
        /// </summary>
        public string description { get; set; }

        /// <summary>
        ///  获券方式：1-免费领取；2-积分兑换；3-花钱购买
        /// </summary>
        public int getWay { get; set; }

        /// <summary>
        /// 时间结束时间
        /// </summary>
        public string useEndTime { get; set; }
        /// <summary>
        ///  使用开始时间
        /// </summary>
        public string useStartTime { get; set; }

        /// <summary>
        /// 券使用门槛
        /// </summary>
        public string useThreshold { get; set; }
        /// <summary>
        /// 是否与其它券叠加
        /// </summary>
        public bool can_overlay_with_other { get; set; }
        /// <summary>
        /// 是否与自身叠加
        /// </summary>
        public bool can_overlay_with_self { get; set; }
        /// <summary>
        /// 优惠券活动id
        /// </summary>
        public string activity_id { get; set; }

    }

    /// <summary>
    /// 优惠券验核Dto
    /// </summary>
    public class couponVerifyListDto : couponBase
    {

    }

    /// <summary>
    /// 优惠券订单明细
    /// </summary>
    public class couponOrderListDto
    {
       
        /// <summary>
        /// 单号
        /// </summary>
        public string TXNNUM { get; set; }

        /// <summary>
        ///  部类
        /// </summary>
        public string DPTCODE { get; set; }

        /// <summary>
        /// 代码
        /// </summary>
        public string CODE { get; set; }

        /// <summary>
        /// 金额
        /// </summary>
        public string PRICE { get; set; }




    }

}
