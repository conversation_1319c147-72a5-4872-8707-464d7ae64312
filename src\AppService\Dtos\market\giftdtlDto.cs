﻿namespace AppService.Dtos.market
{
    /// <summary>
    /// 买赠Dto
    /// </summary>
    public class giftdtlDto
    {
        /// <summary>
        /// 买赠单号
        /// </summary>
        public string GIFTNUM { get; set; }

        /// <summary>
        /// 类型
        /// </summary>
        public string GTYPE { get; set; }

        /// <summary>
        /// 货号.
        /// </summary>
        public string PLUCODE { get; set; }

        /// <summary>
        /// 价格
        /// </summary>
        public int PRICE { get; set; }

        /// <summary>
        /// 折扣
        /// </summary>
        public int DISC { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string REMARKS { get; set; }

        /// <summary>
        /// 门店
        /// </summary>
        public string SHOPID { get; set; }
    }
}
