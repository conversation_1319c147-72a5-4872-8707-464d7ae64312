﻿namespace AppService.Dtos.market
{
    /// <summary>
    /// 买赠Dto
    /// </summary>
    public class gifthdrDto
    {
        /// <summary>
        /// 买赠单号
        /// </summary>
        public string GIFTNUM { get; set; }
        /// <summary>
        /// 买赠名称
        /// </summary>
        public string GIFTNAME { get; set; }
        /// <summary>
        /// 买赠编号
        /// </summary>
        public string PROCODE { get; set; }
        /// <summary>
        /// 级别
        /// </summary>
        public int LVL { get; set; }
        /// <summary>
        /// 买赠类型
        /// </summary>
        public string TXNTYPE { get; set; }
        /// <summary>
        /// 商品类型
        /// </summary>
        public string TYPE { get; set; }
        /// <summary>
        /// 部门编号
        /// </summary>
        public string DPTCODE { get; set; }
        /// <summary>
        /// 分类编号
        /// </summary>
        public string CLSCODE { get; set; }
        /// <summary>
        /// 品牌编号
        /// </summary>
        public string BRAND { get; set; }
        /// <summary>
        /// 陈列架号
        /// </summary>
        public string DISPLAY { get; set; }
        /// <summary>
        /// 代收编号
        /// </summary>
        public string RACODE { get; set; }
        /// <summary>
        /// 货号
        /// </summary>
        public string PLUCODE { get; set; }
        /// <summary>
        /// 会员级别
        /// </summary>
        public string CGRADE { get; set; }
        /// <summary>
        /// 买的数量
        /// </summary>
        public int BUYQTY { get; set; }
        /// <summary>
        /// 买的金额
        /// </summary>
        public int BUYAMT { get; set; }
        /// <summary>
        /// 赠品编号
        /// </summary>
        public string GIFTCODE { get; set; }
        /// <summary>
        /// 赠品数量
        /// </summary>
        public int GIFTQTY { get; set; }
        /// <summary>
        /// 折扣
        /// </summary>
        public int DISC { get; set; }
        /// <summary>
        /// 优惠金额
        /// </summary>
        public int PRICE { get; set; }
        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime? START { get; set; }
        /// <summary>
        /// 结束日期
        /// </summary>
        public DateTime? ENDDT { get; set; }
        /// <summary>
        /// 说明
        /// </summary>
        public string REMARKS { get; set; }
        /// <summary>
        /// 店号
        /// </summary>
        public string SHOPID { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public int? STATUS { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CREDATE { get; set; }
        /// <summary>
        /// 唯一自增
        /// </summary>
        public int UID { get; set; }
    }
}
