﻿using System;

namespace AppService.Dtos.market
{
    /// <summary>
    /// 促销Dto
    /// </summary>
    public class promotionsDto
    {

        /// <summary>
        /// 促销类型
        /// </summary>
        public string TYPE { get; set; }
        /// <summary>
        /// 部类编号
        /// </summary>
        public string DPTCODE { get; set; }
        /// <summary>
        /// 营销分类
        /// </summary>
        public string CLSCODE { get; set; }
        /// <summary>
        /// 陈列分类
        /// </summary>
        public string DISPLAY { get; set; }
        /// <summary>
        /// 品牌
        /// </summary>
        public string BRAND { get; set; }
        /// <summary>
        /// 代收代码
        /// </summary>
        public string RACODE { get; set; }
        /// <summary>
        /// 货号
        /// </summary>
        public string PLUCODE { get; set; }
        /// <summary>
        /// 会员等级
        /// </summary>
        public string CGRADE { get; set; }

        /// <summary>
        /// 折扣
        /// </summary>
        public int DISC { get; set; }

        /// <summary>
        /// 定价
        /// </summary>
        public int PRICE { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? START { get; set; }
        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? ENDDT { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string REMARKS { get; set; }

        public int UID { get; set; }
        /// <summary>
        /// 机号
        /// </summary>
        public string TERMINALID { get; set; }


        /// <summary>
        /// 活动编号
        /// </summary>
        public string PROCODE { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int PROLVL { get; set; }
    }
}
