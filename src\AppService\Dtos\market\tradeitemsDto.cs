﻿namespace AppService.Dtos.market
{
    /// <summary>
    /// 换购Dto
    /// </summary>
    public class tradeitemsDto
    {
        /// <summary>
        /// 换购单号
        /// </summary>
        public string TRADENUM { get; set; }
        /// <summary>
        /// 换购名称
        /// </summary>
        public string TRADENAME { get; set; }
        /// <summary>
        /// 商品类型
        /// </summary>
        public string TYPE { get; set; }

        /// <summary>
        /// 部类
        /// </summary>
        public string DPTCODE { get; set; }

        /// <summary>
        /// 营销分类
        /// </summary>
        public string CLSCODE { get; set; }

        /// <summary>
        /// 品牌
        /// </summary>
        public string BRAND { get; set; }

        /// <summary>
        /// 陈列分类
        /// </summary>
        public string DISPLAY { get; set; }

        /// <summary>
        /// 货号
        /// </summary>
        public string PLUCODE { get; set; }

        /// <summary>
        /// 会员级别
        /// </summary>
        public string CGRADE { get; set; }

        /// <summary>
        /// 购买数量
        /// </summary>
        public int BUYQTY { get; set; }
        /// <summary>
        /// 购买金额
        /// </summary>
        public int BUYAMT { get; set; }

        /// <summary>
        /// 换购条码
        /// </summary>
        public string TRADECODE { get; set; }

        /// <summary>
        /// 换购价格
        /// </summary>
        public int PRICE { get; set; }

        /// <summary>
        /// 换购数量
        /// </summary>
        public int QTY { get; set; }

        /// <summary>
        /// 折扣
        /// </summary>
        public int DISC { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime START { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime ENDDT { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string REMARKS { get; set; }

        /// <summary>
        /// 对应分类
        /// </summary>
        public string CPTSCODE { get; set; }

        /// <summary>
        /// 仓号
        /// </summary>
        public string SHOPID { get; set; }

    }
}
