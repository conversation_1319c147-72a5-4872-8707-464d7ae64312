﻿using System;

namespace AppService.Dtos.member;

/// <summary>
/// 客户信息Dto
/// </summary>
public class customerDto
{

    /// <summary>
    /// 主键
    /// </summary>
    public string id { get; set; }

    /// <summary>
    /// 门店编号
    /// </summary>
    public string store_id { get; set; }

    /// <summary>
    ///  客户编号
    /// </summary>
    public string customer_sn { get; set; }

    /// <summary>
    /// 卡号
    /// </summary>
    public string card_no { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string phone { get; set; }
    /// <summary>
    /// 昵称
    /// </summary>
    public string nick_name { get; set; }
    /// <summary>
    /// 名称
    /// </summary>
    public string real_name { get; set; }

    /// <summary>
    /// 生日
    /// </summary>
    public DateTime? birth_day { get; set; }

    /// <summary>
    ///  性别
    /// </summary>
    public int gender { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public int state { get; set; }

    /// <summary>
    /// openId
    /// </summary>
    public string open_id { get; set; }

    /// <summary>
    /// unionId
    /// </summary>
    public string union_id { get; set; }
}