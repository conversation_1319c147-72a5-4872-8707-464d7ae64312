using System;

namespace AppService.Dtos.member
{
    /// <summary>
    /// 会员黑名单DTO
    /// </summary>
    public class memberBlacklistDto
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        public long id { get; set; }

        /// <summary>
        /// 会员号码（手机号或卡号）
        /// </summary>
        public string CUSTOMER { get; set; }

        /// <summary>
        /// 手机号
        /// </summary>
        public string phone { get; set; }

        /// <summary>
        /// 会员状态：对应 MemberStatusEnum
        /// </summary>
        public int status { get; set; }

        /// <summary>
        /// 原因
        /// </summary>
        public string reason { get; set; }

        /// <summary>
        /// 操作人
        /// </summary>
        public string @operator { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? create_time { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? update_time { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string remark { get; set; }
    }
} 