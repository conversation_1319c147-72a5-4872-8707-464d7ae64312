﻿using System;

namespace AppService.Dtos.member
{
    /// <summary>
    /// 会员信息Dto
    /// </summary>
    public class memberDto
    {
        /// <summary>
        /// 会员号
        /// </summary>
        public string CODE { get; set; }
        /// <summary>
        /// 会员名称
        /// </summary>
        public string MEMBER_NAME { get; set; }
        /// <summary>
        /// 有效期
        /// </summary>
        public DateTime? EXPDATE { get; set; }
        /// <summary>
        /// 等级
        /// </summary>
        public string GRADE { get; set; }
        //public string cardLevelId { get; set; }

        public decimal VAMT { get; set; } = 0;
        /// <summary>
        /// 积分
        /// </summary>
        public int CPTS { get; set; }
        public decimal CREDITLIMIT { get; set; } = 0;

        public decimal USEDOD { get; set; } = 0;

        /// <summary>
        /// 手机号
        /// </summary>
        public string PHONE { get; set; }
        /// <summary>
        /// 生日
        /// </summary>
        public DateTime? BIRTHDAY { get; set; }
        /// <summary>
        /// 折扣
        /// </summary>
        public decimal DISC { get; set; }
        /// <summary>
        /// 生日折扣
        /// </summary>
        public decimal BIRTHDAY_DISC { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public int STATUS { get; set; }
        /// <summary>
        /// 创建日期
        /// </summary>
        public DateTime CREDATE { get; set; }

        /// <summary>
        /// 优惠券数量
        /// </summary>
        public int COUPON_NUM { get; set; } = 0;

        /// <summary>
        /// 有赞open_id
        /// </summary>
        public string OPEN_ID { get; set; }

        /// <summary>
        /// 数据来源
        /// </summary>
        public string SOURCE { get; set; }

    }

    /// <summary>
    /// 会员信息Dto
    /// </summary>
    public class memberDtoList
    {
        /// <summary>
        /// 会员号
        /// </summary>
        public string CODE { get; set; }
        /// <summary>
        /// 会员名称
        /// </summary>
        public string MEMBER_NAME { get; set; }
        /// <summary>
        /// 有效期
        /// </summary>
        public DateTime? EXPDATE { get; set; }
        /// <summary>
        /// 等级
        /// </summary>
        public string GRADE { get; set; }
        //public string cardLevelId { get; set; }

        public decimal VAMT { get; set; } = 0;
        /// <summary>
        /// 积分
        /// </summary>
        public int CPTS { get; set; }
        public decimal CREDITLIMIT { get; set; } = 0;

        public decimal USEDOD { get; set; } = 0;

        /// <summary>
        /// 手机号
        /// </summary>
        public string PHONE { get; set; }
        /// <summary>
        /// 生日
        /// </summary>
        public DateTime? BIRTHDAY { get; set; }
        /// <summary>
        /// 折扣
        /// </summary>
        public decimal DISC { get; set; }
        /// <summary>
        /// 生日折扣
        /// </summary>
        public decimal BIRTHDAY_DISC { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public int STATUS { get; set; }
 

    }

    /// <summary>
    /// 会员等级Dto
    /// </summary>
    public class memberGradeDto
    {
        /// <summary>
        /// 等级编号
        /// </summary>
        public string GRADE { get; set; }

        public decimal TTLAMT1 { get; set; }

        public decimal TTLAMT2 { get; set; }

        public int VPT { get; set; }

        public decimal DISCOUNT { get; set; }
        /// <summary>
        /// 描述
        /// </summary>
        public string DESCRIPT { get; set; }

    }

    /// <summary>
    /// 灵智会员等级
    /// </summary>
    public class memberLzGradeDto
    {
        /// <summary>
        /// 等级编号
        /// </summary>
        public string Grade { get; set; }

        /// <summary>
        /// 灵智会员等级编号
        /// </summary>
        public string lzCard { get; set; }

        /// <summary>
        /// 折扣
        /// </summary>
        public decimal DISCOUNT { get; set; }

        /// <summary>
        /// 折扣描述
        /// </summary>
        public string DESCRIPT { get; set; }

    }
}
