﻿namespace AppService.Dtos.order
{
    /// <summary>
    /// 门店订单传输对象
    /// </summary>
    public class shopTxnsDto
    {

        /// <summary>
        /// 单号
        /// </summary>
        public string TXNID { get; set; }

        /// <summary>
        /// 单号
        /// </summary>
        public string TXNNUM { get; set; }

        /// <summary>
        /// 仓号
        /// </summary>
        public string SHOPID { get; set; }

        /// <summary>
        /// 机号
        /// </summary>
        public string TERMINAL { get; set; }

        /// <summary>
        /// 班次号
        /// </summary>
        public string SHIFT { get; set; }

        /// <summary>
        /// 操作员
        /// </summary>
        public string OPERATOR { get; set; }

        /// <summary>
        /// 交易时间
        /// </summary>
        public DateTime TXNTIME { get; set; }

        /// <summary>
        /// 单号
        /// </summary>
        public long VOUCHNUM { get; set; }

        /// <summary>
        /// 序号
        /// </summary>
        public short SEQUENCE { get; set; }

        /// <summary>
        /// 交易类型 0- 支付，1-退款
        /// </summary>
        public short TXNTYPE { get; set; }

        /// <summary>
        /// 销售类型
        /// </summary>
        public short SALESTYPE { get; set; }

        /// <summary>
        /// 部类号
        /// </summary>
        public string DPTCODE { get; set; }

        /// <summary>
        /// 营销分类号
        /// </summary>
        public string CLSCODE { get; set; }

        /// <summary>
        /// 陈列分类
        /// </summary>
        public string DISPLAY { get; set; }

        /// <summary>
        /// 品牌
        /// </summary>
        public string BRAND { get; set; }

        /// <summary>
        /// 代码
        /// </summary>
        public string CODE { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public int QTY { get; set; }

        /// <summary>
        /// 单价
        /// </summary>
        public int PRICE { get; set; }
        /// <summary>
        /// 折扣
        /// </summary>
        public int DISC { get; set; }
        /// <summary>
        /// 码洋
        /// </summary>
        public int ORGAMT { get; set; }

        /// <summary>
        /// 明细折扣额
        /// </summary>
        public int ITEMDISC { get; set; }

        /// <summary>
        /// 整单折扣
        /// </summary>
        public int TTLDISC { get; set; }

        public int REAL { get; set; }

        /// <summary>
        /// 会员号
        /// </summary>
        public string MEMBER { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        public short INCTAX { get; set; }

        public string SALES { get; set; }

        public string DISCREM { get; set; }

        /// <summary>
        /// 活动代码
        /// </summary>
        public string PROCODE { get; set; }

        /// <summary>
        /// 手机号
        /// </summary>
        public string PHONE { get; set; }

        /// <summary>
        /// 积分倍率
        /// </summary>
        public int CPTS { get; set; } = 1;


    }
}
