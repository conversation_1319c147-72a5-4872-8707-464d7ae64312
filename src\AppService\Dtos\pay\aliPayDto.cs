﻿namespace AppService.Dtos.pay
{

    /// <summary>
    /// 支付宝配置Dto
    /// </summary>
    public class aliPayDto
    {
        /// <summary> 
        /// 应用编号 
        /// </summary> 
        /// <returns></returns> 
        public string alipay_app_id { get; set; }
        /// <summary> 
        /// 客户私钥 
        /// </summary> 
        /// <returns></returns> 
        public string merchant_private_key { get; set; }
        /// <summary> 
        /// 商户公钥 
        /// </summary> 
        /// <returns></returns> 
        public string merchant_public_key { get; set; }
        /// <summary> 
        /// 阿里公钥 
        /// </summary> 
        /// <returns></returns> 
        public string alipay_public_key { get; set; }
        /// <summary> 
        /// 系统商编号
        /// </summary> 
        /// <returns></returns> 
        public string sys_service_provider_id { get; set; }  
        /// <summary> 
        /// 签名加密类型（固定值：1.RSA2、2.RSA） 
        /// </summary> 
        /// <returns></returns> 
        public string sign_type { get; set; }

    }
}
