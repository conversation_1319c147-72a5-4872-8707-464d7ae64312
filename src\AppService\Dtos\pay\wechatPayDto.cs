﻿namespace AppService.Dtos.pay
{


    /// <summary>
    /// 门店微信配置Dto
    /// </summary>
    public class wechatPayDto
    {
        /// <summary> 
        /// app_id 
        /// </summary> 
        /// <returns></returns> 
        public string app_id { get; set; }
        /// <summary> 
        /// mch_id 
        /// </summary> 
        /// <returns></returns> 
        public string mch_id { get; set; }
        /// <summary> 
        /// sub_appid 
        /// </summary> 
        /// <returns></returns> 
        public string sub_appid { get; set; }
        /// <summary> 
        /// sub_mch_id 
        /// </summary> 
        /// <returns></returns> 
        public string sub_mch_id { get; set; }
        /// <summary> 
        /// pay_key 
        /// </summary> 
        /// <returns></returns> 
        public string pay_key { get; set; }
        /// <summary> 
        /// 公众号支付回调地址 
        /// </summary> 
        /// <returns></returns> 
        public string notify_url { get; set; }

        /// <summary> 
        /// 证书路径 
        /// </summary> 
        /// <returns></returns> 
        public string cert_path { get; set; }

    }
}
