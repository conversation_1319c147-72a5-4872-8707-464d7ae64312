﻿namespace AppService.Dtos.product
{
    /// <summary>
    /// 陈列分类传输对象
    /// </summary>
    public class displayDto
    {
        /// <summary>
        /// 分类代码
        /// </summary>
        public string DSPCODE { get; set; }
        /// <summary>
        /// 分类名称
        /// </summary>
        public string DSPNAME { get; set; }
        /// <summary>
        /// 分类折扣
        /// </summary>
        public int DISC { get; set; }
        /// <summary>
        /// 终端机号
        /// </summary>
        public string TERMINALID { get; set; }
    }
}
