﻿using System;

namespace AppService.Dtos.product
{
    //商品传输dto
    public class pluDto
    {
        /// <summary>
        /// 货号
        /// </summary>
        public string PLUCODE { get; set; }
        /// <summary>
        /// 名称
        /// </summary>
        public string TITLE { get; set; }
        /// <summary>
        /// 部类编号
        /// </summary>
        public string DPTCODE { get; set; }
        /// <summary>
        /// 营销分类
        /// </summary>
        public string CLSCODE { get; set; }
        /// <summary>
        /// 陈列分类
        /// </summary>
        public string DISPLAY { get; set; }
        /// <summary>
        /// 品牌
        /// </summary>
        public string BRAND { get; set; }      
        /// <summary>
        /// 定价
        /// </summary>
        public int PRICE { get; set; }
        /// <summary>
        /// 折扣
        /// </summary>
        public int DISC { get; set; }
        /// <summary>
        /// 定价1
        /// </summary>
        public int PRICE1 { get; set; }
        /// <summary>
        /// 定价2
        /// </summary>
        public int PRICE2 { get; set; }
        /// <summary>
        /// 定价3
        /// </summary>
        public int PRICE3 { get; set; }
        /// <summary>
        /// 终端号
        /// </summary>
        public string TERMINALID { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CREDATE { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public int STALE { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string MSG { get; set; }
        /// <summary>
        /// 税率
        /// </summary>
        public int? INCTAX { get; set; }
    }
}
