﻿namespace AppService.Dtos.product
{
    /// <summary>
    ///  代收dto
    /// </summary>
    public class raDto
    {
        /// <summary>
        /// 代收代码
        /// </summary>
        public string RACODE { get; set; }

        /// <summary>
        /// 代收名称
        /// </summary>
        public string RANAME { get; set; }

        /// <summary>
        /// 价格
        /// </summary>
        public decimal? PRICE { get; set; }

        /// <summary>
        /// 折扣
        /// </summary>
        public int DISC { get; set; }

        /// <summary>
        /// 终端号
        /// </summary>
        public string TERMINALID { get; set; }
        /// <summary>
        /// 营销分类
        /// </summary>
        public string CLSCODE { get; set; }
    }
}
