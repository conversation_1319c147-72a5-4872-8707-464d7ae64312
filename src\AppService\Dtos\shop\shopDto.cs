﻿namespace AppService.Dtos.shop;

/// <summary>
/// 门店传输对象
/// </summary>
public class shopDto
{
    /// <summary>
    /// 门店编号
    /// </summary>
    public string id { get; set; }

    /// <summary>
    /// 门店编号
    /// </summary>
    public string store_no { get; set; }

    /// <summary>
    /// 门店名称
    /// </summary>
    public string store_name { get; set; }

    /// <summary>
    /// 门店名称
    /// </summary>
    public int? status { get; set; }

    /// <summary>
    /// 外部门店id
    /// </summary>
    public string source_no { get; set; }

    /// <summary>
    /// 上级门店
    /// </summary>
    public string parent_id { get; set; }
}
