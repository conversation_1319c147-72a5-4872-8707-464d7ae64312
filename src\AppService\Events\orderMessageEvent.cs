﻿using BPM.Events;
using BPM.Events.Messages;
using Domain.Models.order;

namespace AppService.Events;

/// <summary>
/// 用户登录 消息事件
/// </summary>
public class orderMessageEvent : MessageEvent<orderMessage>
{
    /// <summary>
    /// 初始化一个<see cref="UserLoginMessageEvent"/>类型的实例
    /// </summary>
    /// <param name="data">数据</param>
    public orderMessageEvent(orderMessage data) : base(data)
    {
        Send = true;
        Name = "orderPurgEvent";
    }
}

/// <summary>
/// 用户登录消息
/// </summary>
public class orderMessage : IEventSession
{
    /// <summary>
    /// 会话标识
    /// </summary>
    public string SessionId { get; set; }
    /// <summary>
    /// 机号
    /// </summary>
    public string TERMINAL { get; set; }
    /// <summary>
    /// 交易日期
    /// </summary>
    public string TXNTIME { get; set; }

    /// <summary>
    ///  订单明细
    /// </summary>
    public List<shopTxnsEntity> items { get; set; }
}

