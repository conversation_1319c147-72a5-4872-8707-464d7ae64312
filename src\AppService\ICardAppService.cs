﻿using AppService.Dtos.card;

namespace AppService
{
    /// <summary>
    /// 版 本 BPM敏捷开发框架
    
    /// 创建人：Aarons
    /// 日 期：2022-09-05
    /// 描 述：储值卡应用服务接口
    /// </summary>
    public interface ICardAppService : IAppService
    {
        /// <summary>
        /// 获取储值卡信息
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<cardDto> getCardInfo(cardQueryRequest request);

        /// <summary>
        /// 获取储值卡制卡信息
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<cardVFDto> getCardVFInfo(cardvfRequest request);

        /// <summary>
        /// 获取储值卡面值列表
        /// </summary>
        /// <param name="shop_id">门店编号</param>
        /// <returns></returns>
        Task<List<cardFaceDto>> getCardFaceList(string shop_id);

        /// <summary>
        /// 保存储值卡交易记录
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task saveCardTxns(cardTxnsRequest request);

        /// <summary>
        /// 储值卡充值
        /// </summary>
        /// <param name="request"></param>
        /// <param name="vf_code"></param>
        /// <param name="vf_paas_word"></param>
        /// <returns></returns>
        Task cardRecharge(cardRechargeRequest request, string vf_code, string vf_paas_word);
    }
}
