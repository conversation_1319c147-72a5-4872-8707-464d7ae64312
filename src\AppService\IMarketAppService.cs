﻿using AppService.Dtos.market;

namespace AppService
{
    /// <summary>
    /// Author：Aarons
    /// Create date：2022-07-05
    /// Description：促销应用服务接口
    /// </summary>
    public interface IMarketAppService
    {
        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2022-07-05
        /// Description:  获取促销活动明细
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns></returns>
        Task<PagerList<promotionsDto>> getPagePromotionsList(promotionsQuery query);

        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2022-10-24
        /// Description:  获取买赠活动明细
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns></returns>
        Task<PagerList<gifthdrDto>> getPageGifthdrList(promotionsQuery query);


        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2023-02-22
        /// Description:  获取买赠活动明细
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns></returns>
        Task<PagerList<giftdtlDto>> getPageGiftdtlList(promotionsQuery query);

        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2023-03-10
        /// Description:  获取换购明细
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns></returns>
        Task<PagerList<tradeitemsDto>> getPageTradeItemsList(promotionsQuery query);


    }
}
