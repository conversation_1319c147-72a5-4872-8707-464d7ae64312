﻿using AppService.Dtos.member;

namespace AppService
{
    /// <summary>
    /// Author：Aarons
    /// Create date：2022-07-05
    /// Description：会员应用服务接口
    /// </summary>
    public interface IMemberAppService
    {
        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2022-07-05
        /// Description:  会员信息
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns></returns>
        Task<PagerList<memberDto>> getPageMemberList(memberQuery query);
        Task<PagerList<memberDtoList>> getPageMemberListNew(memberQuery query);

        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2022-07-05
        /// Description:  会员等级信息
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns></returns>
        Task<PagerList<memberGradeDto>> getPageMemberGradeList(memberQuery query);

        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2022-07-09
        /// Description:  会员信息
        /// </summary>
        /// <param name="request">查询条件</param>
        /// <returns></returns>
        Task<List<memberDto>> getMemberInfo(memberRequest request);

        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2022-07-05
        /// Description:  灵智会员等级信息
        /// </summary>
        Task<List<memberLzGradeDto>> getMemberYzGradeList();

        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2024-11-25-02
        /// Description:  获取客户权益
        /// </summary>
        /// <param name="request">查询条件</param>
        /// <returns></returns>
        Task<customerEquityDto> getCustomerEquity(string member_card_no);

        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2024-11-25-02
        /// Description:  获取客户
        /// </summary>
        /// <param name="phone">查询条件</param>
        /// <returns></returns>
        Task<customerDto> getCustomerInfo(string phone);

        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2024-03-19
        /// Description:  获取会员黑名单信息
        /// </summary>
        /// <param name="code">会员号码或手机号</param>
        /// <returns></returns>
        Task<memberBlacklistDto> GetMemberBlacklist(string code);
    }
}
