﻿using BPM.Permissions.Identity.JwtBearer;

namespace AppService
{
    /// <summary>
    /// 安全服务
    /// </summary>
    public interface IOAuthAppService : IAppService
    {
        /// <summary>
        /// 登录
        /// </summary>
        /// <param name="request">请求</param>
        Task<SignInWithTokenResult> SignInAsync(AppInfo dto);
        /// <summary>
        /// 刷新令牌
        /// </summary>
        /// <param name="refreshToken">刷新令牌</param>
        Task<JsonWebToken> RefreshTokenAsync(string refreshToken);
    }
}
