﻿using AppService.Dtos.order;
using System.Data;

namespace AppService
{
    /// <summary>
    /// 版 本 BPM敏捷开发框架
    
    /// 创建人：Aarons
    /// 日 期：2022-07-09
    /// 描 述：订单应用服务接口
    /// </summary>
    public interface IOrderAppService : IAppService
    {
        /// <summary>
        /// 获取交易订单明细
        /// </summary>
        /// <param name="txnnum"></param>
        /// <returns></returns>
        Task<List<shopTxnsDto>> getshopTxnsList(string txnnum);

        /// <summary>
        /// 保存订单
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns></returns>
        Task<int> saveOrder(orderRequest request);

        /// <summary>
        /// 订单清机操作
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns></returns>
        Task<int> savePurgingOrder(orderPurgRequest request);

        /// <summary>
        /// 根据支付代码查询
        /// </summary>
        /// <param name="pay_code"></param>
        /// <returns></returns>
        Task<shopTxnsDto> GetShopTxnsByPayCode(string pay_code);

        /// <summary>
        ///  获取未同步的记录
        /// </summary>
        /// <param name="shop_id">订单号</param>
        /// <param name="txntime">日期</param>
        /// <returns></returns>
        Task<DataTable> NotSyncTxnumDt(string shop_id, string txntime);
    }
}
