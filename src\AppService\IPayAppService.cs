﻿using AppService.Dtos.pay;

namespace AppService
{
    /// <summary>
    /// Author：Aarons
    /// Create date：2022-07-05
    /// Description：支付应用服务接口
    /// </summary>
    public interface IPayAppService
    {
        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2022-07-05
        /// Description:  获取支付方式列表
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns></returns>
        Task<PagerList<paymethodDto>> getPagePaymethodList(baseQuery query);

        /// <summary>
        /// 获取门店微信支付
        /// </summary>
        /// <param name="shop_id">门店编号</param>
        /// <returns></returns>
        Task<wechatPayDto> getShopWechatPay(string store_id);

        /// <summary>
        /// 获取门店支付宝支付
        /// </summary>
        /// <param name="shop_id">门店编号</param>
        /// <returns></returns>
        Task<aliPayDto> getShopAliPay(string store_id);

        /// <summary>
        /// 保存支付日志
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        Task savePayTradeLog(payTradeLogDto dto);
        /// <summary>
        /// 获取交易信息
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<List<payTradeLogDto>> getPayTradeLog(payLogsQuery query);
    }
}
