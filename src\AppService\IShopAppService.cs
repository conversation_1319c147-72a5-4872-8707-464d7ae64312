﻿using AppService.Dtos.shop;

namespace AppService
{
    /// <summary>
    /// Author：Aarons
    /// Create date：2022-10-25
    /// Description：门店应用服务
    /// </summary>
    public interface IShopAppService
    {

        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2022-10-25
        /// Description:  获取终端列表
        /// </summary>
        /// <param name="request">查询条件</param>
        /// <returns></returns>
        Task<List<terminalDto>> getTerminalList();

        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2022-10-25
        /// Description:  获取终端信息
        /// </summary>
        /// <param name="request">查询条件</param>
        /// <returns></returns>
        Task<terminalDto> getTerminalInfo(terminalRequest request);


        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2022-10-25
        /// Description:  获取终端信息
        /// </summary>
        /// <param name="request">查询条件</param>
        /// <returns></returns>
        Task<terminalDto> getTerminalInfoByMac(string mac);

        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2024-10-25
        /// Description:  获取门店信息
        /// </summary>
        /// <param name="request">查询条件</param>
        /// <returns></returns>
        Task<shopDto> getShopInfoById(string shop_id);

    }
}
