﻿using Domain.Models.app;

namespace AppService.Impl
{
    /// <summary>
    /// 安全服务
    /// </summary>
    public class ApplicationService : AppServiceBase, IApplicationService
    {
        /// <summary>
        /// Sql查询对象
        /// </summary>
        protected ISqlQuery sqlQuery { get; set; }
        /// <summary>
        /// 初始化应用服务
        /// </summary>
        public ApplicationService(ISqlQuery _sqlQuery)
        {
            sqlQuery = _sqlQuery;
        }

        #region 获取数据
        /// <summary>
        /// Author:		  Even
        /// Create date:  2021-08-15 11:53:41.140
        /// Description:  获取应用信息
        /// </summary>
        /// <param name="app_id">应用编号</param>
        /// <returns></returns>
        public async Task<appAccountDto> GetAppAccountInfo(string app_id)
        {
            return await sqlQuery.Select("a.*").From<appAccountEntity>("a")
                 .Where<appAccountEntity>(t => t.app_id, app_id, BPM.Operator.Equal)
                .ToAsync<appAccountDto>();
        }
        #endregion
    }
}
