﻿using AppService.Dtos.market;
using Domain.Models.market;

namespace AppService.Impl
{
    /// <summary>
    /// Author：Aarons
    /// Create date：2022-07-05
    /// Description：活动应用服务
    /// </summary>
    public class MarketAppService : AppServiceBase, IMarketAppService
    {
        /// <summary>
        /// Sql查询对象
        /// </summary>
        protected ISqlQuery sqlQuery { get; set; }
        /// <summary>
        /// 初始化应用服务
        /// </summary>
        public MarketAppService(ISqlQuery _sqlQuery)
        {
            sqlQuery = _sqlQuery;
        }

        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2022-07-05
        /// Description:  获取促销活动明细
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns></returns>
        public async Task<PagerList<promotionsDto>> getPagePromotionsList(promotionsQuery query)
        {
            string[] array = { query.terminal_id, "-" };
            return await sqlQuery.From<promotionsEntity>("a")
                 .Where<promotionsEntity>(x => x.TERMINALID, array, BPM.Operator.In)
                 .ToPagerListAsync<promotionsDto>(new Pager(query.Page, query.PageSize, query.Order));
        }


        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2022-10-24
        /// Description:  获取买赠活动
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns></returns>
        public async Task<PagerList<gifthdrDto>> getPageGifthdrList(promotionsQuery query)
        {
            string[] array = { query.shop_id, "-" };
            return await sqlQuery.From<gifthdrEntity>("a")
                 .Where<gifthdrEntity>(x => x.SHOPID, array, BPM.Operator.In)
                 .ToPagerListAsync<gifthdrDto>(new Pager(query.Page, query.PageSize, query.Order));
        }

        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2023-02-22
        /// Description:  获取买赠活动明细
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns></returns>
        public async Task<PagerList<giftdtlDto>> getPageGiftdtlList(promotionsQuery query)
        {
            string[] array = { query.shop_id, "-" };
            return await sqlQuery.From<giftdtlEntity>("a")
                 .Where<giftdtlEntity>(x => x.SHOPID, array, BPM.Operator.In)
                 .ToPagerListAsync<giftdtlDto>(new Pager(query.Page, query.PageSize, query.Order));
        }


        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2023-03-10
        /// Description:  获取换购明细
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns></returns>
        public async Task<PagerList<tradeitemsDto>> getPageTradeItemsList(promotionsQuery query)
        {
            string[] array = { query.shop_id, "-" };
            return await sqlQuery.From<tradeitemsEntity>("a")
                 .Where<tradeitemsEntity>(x => x.SHOPID, array, BPM.Operator.In)
                 .ToPagerListAsync<tradeitemsDto>(new Pager(query.Page, query.PageSize, query.Order));
        }

    }
}
