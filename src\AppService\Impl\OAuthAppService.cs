﻿using BPM.Permissions.Identity.JwtBearer;
using BPM.Permissions.Identity.Results;

namespace AppService.Impl
{
    /// <summary>
    /// 安全服务
    /// </summary>
    public class OAuthAppService : AppServiceBase, IOAuthAppService
    {

        /// <summary>
        /// Jwt令牌构建器
        /// </summary>
        protected IJsonWebTokenBuilder TokenBuilder { get; }

        /// <summary>
        /// Sql查询对象
        /// </summary>
        protected ISqlQuery sqlQuery { get; set; }

        /// <summary>
        /// 初始化服务
        /// </summary>
        /// <param name="unitOfWork"></param>
        /// <param name="tokenBuilder"></param>
        public OAuthAppService(IJsonWebTokenBuilder tokenBuilder, ISqlQuery _sqlQuery)
        {
            TokenBuilder = tokenBuilder;
            sqlQuery = _sqlQuery;
        }

        /// <summary>
        /// 登录
        /// </summary>
        /// <param name="request">请求</param>
        public async Task<SignInWithTokenResult> SignInAsync(AppInfo app)
        {
            var user = app.MapTo<baseUser>();
            AddClaimsToUser(user);
            var result = new SignInResult() { UserId = user.UserId, State = SignInState.Succeeded, Message = "登录成功" };
            return await GetLoginResultAsync(user, result);
        }


        /// <summary>
        /// 添加声明到用户
        /// </summary>
        /// <param name="user">用户</param>
        /// <param name="applicationCode">应用程序编码</param>
        private void AddClaimsToUser(baseUser user)
        {
            user.AddUserClaims();
        }

        /// <summary>
        /// 获取登录结果
        /// </summary>
        /// <param name="user">用户</param>
        /// <param name="signInResult">登录结果</param>
        private async Task<SignInWithTokenResult> GetLoginResultAsync(baseUser user, SignInResult signInResult)
        {
            if (signInResult.State == SignInState.Failed)
                return new SignInWithTokenResult { UserId = signInResult.UserId, State = signInResult.State, Message = signInResult.Message };
            //先获取当前用户的缓存token
            var deviceTaoken = await TokenBuilder.GetUserDeviceTokenAsync(user.AppId, "admin");
            if (!deviceTaoken.IsNull())
            {
                //验证token是否过期
                var IsExpired = deviceTaoken.Token.IsExpired();
                if (!IsExpired)
                {
                    return new SignInWithTokenResult
                    {
                        UserId = signInResult.UserId,
                        State = signInResult.State,
                        Message = signInResult.Message,
                        Token = deviceTaoken.Token
                    };
                }
            }
            //颁发当前用户token
            var result = await TokenBuilder.CreateAsync(user.GetClaims().ToDictionary(x => x.Type, x => x.Value));
            return new SignInWithTokenResult
            {
                UserId = signInResult.UserId,
                State = signInResult.State,
                Message = signInResult.Message,
                Token = result
            };
        }

        /// <summary>
        /// 刷新令牌
        /// </summary>
        /// <param name="refreshToken">刷新令牌</param>
        public async Task<JsonWebToken> RefreshTokenAsync(string refreshToken)
        {
            var result = await TokenBuilder.RefreshAsync(refreshToken);
            return result;
        }


    }
}
