﻿using AppService.Dtos.order;
using AppService.Events;
using BPM.Events.Messages;
using BPM.Mapper;
using Domain.Models.order;
using Domain.order.Repository;
using System.Data;
using System.Threading;

namespace AppService.Impl
{

    /// <summary>
    /// 版 本 BPM敏捷开发框架

    /// 创建人：Aarons
    /// 日 期：2022.03.18
    /// 描 述：订单应用服务
    /// </summary>
    public class OrderAppService : AppServiceBase, IOrderAppService
    {

        /// <summary>
        /// 订单仓储接口
        /// </summary>
        private readonly IOrderRepository _orderRepository;

        /// <summary>
        /// 工作单元
        /// </summary>
        private readonly IUnitOfWork _unitOfWork;

        /// <summary>
        /// sql 执行器
        /// </summary>
        protected ISqlExecutor _sqlExecutor;

        /// <summary>
        /// Sql查询对象
        /// </summary>
        protected ISqlQuery _sqlQuery { get; set; }

        /// <summary>
        /// 消息事件总线
        /// </summary>
        public IMessageEventBus _eventBus { get; }

        /// <summary>
        /// 初始化应用服务
        /// </summary>
        /// <param name="orderRepository">订单仓储</param>
        public OrderAppService(IOrderRepository orderRepository, ISqlExecutor sqlExecutor, IUnitOfWork unitOfWork
            , IMessageEventBus eventBus, ISqlQuery sqlQuery)
        {
            _orderRepository = orderRepository;
            _unitOfWork = unitOfWork;
            _sqlExecutor = sqlExecutor;
            _eventBus = eventBus;
            _sqlQuery = sqlQuery;
        }

        /// <summary>
        /// 获取交易订单明细
        /// </summary>
        /// <param name="txnnum"></param>
        /// <returns></returns>
        public async Task<List<shopTxnsDto>> getshopTxnsList(string txnnum)
        {
            return (await _orderRepository.getShopTxnsList(txnnum)).MapToList<shopTxnsDto>();
        }

        /// <summary>
        /// 保存订单
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns></returns>
        public async Task<int> saveOrder(orderRequest request)
        {
            try
            {
                Log.Info($"【订单保存】开始处理订单，订单号:{request.TXNNUM}，线程ID:{Thread.CurrentThread.ManagedThreadId}");
                
                var entitys = await _orderRepository.FindAllNoTrackingAsync(x => x.TXNNUM.Equals(request.TXNNUM));
                if (!entitys.IsNull() && entitys.Count > 0)
                {
                    Log.Info($"【订单保存】订单已存在，准备删除旧数据，订单号:{request.TXNNUM}，数量:{entitys.Count}");
                    await _orderRepository.RemoveAsync(entitys);
                    Log.Info($"【订单保存】旧数据删除完成，订单号:{request.TXNNUM}");
                    //await _sqlExecutor.ExecuteSqlAsync("DELETE SHOPTXNS WHERE TXNNUM=@TXNNUM", new { TXNNUM = request.TXNNUM });
                }
                
                Log.Info($"【订单保存】解析订单数据，订单号:{request.TXNNUM}");
                var items = request.items.ToObject<List<shopTxnsEntity>>();
                foreach (var item in items)
                    item.Init();
                
                request.is_sale = items.Where(x => x.TXNTYPE == 0).Count() > 0 ? true : false;
                Log.Info($"【订单保存】订单类型判断，订单号:{request.TXNNUM}，is_sale:{request.is_sale}，订单项数量:{items.Count}");
                
                //插入订单
                Log.Info($"【订单保存】开始保存订单数据，订单号:{request.TXNNUM}");
                await _orderRepository.AddAsync(items);
                Log.Info($"【订单保存】订单数据已添加到仓储，准备提交事务，订单号:{request.TXNNUM}");
                
                //工作单元
                await _unitOfWork.CommitAsync();
                Log.Info($"【订单保存】订单数据保存完成，事务已提交，订单号:{request.TXNNUM}");
                
                return items.Count;
            }
            catch (Exception ex)
            {
                Log.Error($"【订单保存】保存订单异常，订单号:{request.TXNNUM}，异常:{ex.Message}，堆栈:{ex.StackTrace}");
                throw; // 重新抛出异常
            }
        }

        /// <summary>
        /// 订单清机操作
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns></returns>
        public async Task<int> savePurgingOrder(orderPurgRequest request)
        {
            //await _sqlExecutor.ExecuteSqlAsync("DELETE SHOPTXNS WHERE TERMINAL=@TERMINAL AND CONVERT(VARCHAR(20),TXNTIME,23)=@TXNTIME", new { TERMINAL = request.TERMINAL, TXNTIME = request.TXNTIME });
            var items = request.items.ToObject<List<shopTxnsEntity>>();

            ////插入订单
            //await _orderRepository.AddAsync(items);
            ////工作单元
            //await _unitOfWork.CommitAsync();
            // 发布订阅消息
            var message = new orderMessage();
            message.TERMINAL = request.TERMINAL;
            message.TXNTIME = request.TXNTIME;
            message.items = items;
            await _eventBus.PublishAsync(new orderMessageEvent(message));
            return items.Count;
        }



        /// <summary>
        /// 根据支付代码查询
        /// </summary>
        /// <param name="pay_code"></param>
        /// <returns></returns>
        public async Task<shopTxnsDto> GetShopTxnsByPayCode(string pay_code)
        {
            var entity = await _orderRepository.SingleAsync(p => p.CODE.Equals(pay_code));
            return entity.MapTo<shopTxnsDto>();
        }




        /// <summary>
        ///  获取未同步的记录
        /// </summary>
        /// <param name="shop_id">订单号</param>
        /// <param name="txntime">日期</param>
        /// <returns></returns>
        public async Task<DataTable> NotSyncTxnumDt(string shop_id, string txntime)
        {
            var strSql = new StringBuilder();
            strSql.Append(@$"SELECT a.TXNNUM,a.TXNTYPE FROM SHOPTXNS(NOLOCK) a
                                  LEFT JOIN SyncLogs(NOLOCK) b ON a.TXNNUM=b.SourceId AND a.SHOPID=b.ShopId AND a.TERMINAL=b.DeviceId AND b.SyncDate>='{txntime}'  
                                  WHERE a.MEMBER<>'' AND b.SourceId IS NULL AND SALESTYPE=8 AND a.TXNTIME>='{txntime}'
                                  GROUP BY TXNNUM,TXNTYPE");
            var reader = await _sqlExecutor.ExecuteReaderAsync(strSql.ToString());
            return DataConvertHelper.ToDataTable(reader);

        }


    }
}
