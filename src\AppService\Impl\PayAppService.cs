﻿using AppService.Dtos.pay;
using Domain.Models.pay;
using Domain.order.Repository;

namespace AppService.Impl
{
    /// <summary>
    /// Author：Aarons
    /// Create date：2022-07-05
    /// Description：支付应用服务
    /// </summary>
    public class PayAppService : AppServiceBase, IPayAppService
    {

        /// <summary>
        /// 订单仓储接口
        /// </summary>
        private readonly IOrderRepository _orderRepository;
        /// <summary>
        /// Sql查询对象
        /// </summary>
        protected ISqlQuery sqlQuery { get; set; }
        /// <summary>
        /// 初始化应用服务
        /// </summary>
        public PayAppService(ISqlQuery _sqlQuery, IOrderRepository orderRepository)
        {
            sqlQuery = _sqlQuery;
            _orderRepository = orderRepository;
        }


        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2022-07-05
        /// Description:  获取支付方式列表
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns></returns>
        public async Task<PagerList<paymethodDto>> getPagePaymethodList(baseQuery query)
        {
            return await sqlQuery.From<paymethodEntity>("a").ToPagerListAsync<paymethodDto>(new Pager(query.Page, query.PageSize, query.Order));
        }

        /// <summary>
        /// 获取门店微信支付
        /// </summary>
        /// <param name="shop_id">门店编号</param>
        /// <returns></returns>
        public async Task<wechatPayDto> getShopWechatPay(string shop_id)
        {
            var entity = await sqlQuery.Select<wechatPayEntity>(true)
                   .From<wechatPayEntity>("a")
                   .WhereIfNotEmpty<wechatPayEntity>(t => t.shop_id, shop_id)
                   .ToAsync<wechatPayEntity>();
            return entity.MapTo<wechatPayDto>();
        }

        /// <summary>
        /// 获取门店支付宝支付
        /// </summary>
        /// <param name="shop_id">门店编号</param>
        /// <returns></returns>
        public async Task<aliPayDto> getShopAliPay(string shop_id)
        {
            return await sqlQuery.Select<aliPayDto>(true)
                   .From<alipayEntity>("a")
                   .WhereIfNotEmpty<alipayEntity>(t => t.shop_id, shop_id)
                   .ToAsync<aliPayDto>();
        }


       
        /// <summary>
        /// 获取交易信息
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<List<payTradeLogDto>> getPayTradeLog(payLogsQuery query)
        {
            return await sqlQuery.Select<payTradeLogDto>(true)
                   .From<payTradeLogEntity>("a")
                   .WhereIfNotEmpty<payTradeLogEntity>(t => t.pay_sn, query.pay_sn)
                   .WhereIfNotEmpty<payTradeLogEntity>(t => t.trade_type, query.trade_type)
                   .ToListAsync<payTradeLogDto>();
        }

        /// <summary>
        /// 保存支付日志
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public async Task savePayTradeLog(payTradeLogDto dto)
        {
            var entity = dto.MapTo<payTradeLogEntity>();
            entity.id=Guid.NewGuid().ToString();
            entity.created_date=DateTime.Now;
            entity.trade_date = DateTime.Now;
            await _orderRepository.savePayTradeLog(entity);
        }

    }
}
