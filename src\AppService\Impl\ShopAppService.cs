﻿using AppService.Dtos.shop;
using Azure.Core;
using Domain.Models.shop;

namespace AppService.Impl
{
    /// <summary>
    /// Author：Aarons
    /// Create date：2022-10-25
    /// Description：活动应用服务
    /// </summary>
    public class ShopAppService : AppServiceBase, IShopAppService
    {
        /// <summary>
        /// Sql查询对象
        /// </summary>
        protected ISqlQuery sqlQuery { get; set; }
        /// <summary>
        /// 初始化应用服务
        /// </summary>
        public ShopAppService(ISqlQuery _sqlQuery)
        {
            sqlQuery = _sqlQuery;
        }

        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2022-10-25
        /// Description:  获取终端列表
        /// </summary>
        /// <param name="request">查询条件</param>
        /// <returns></returns>
        public async Task<List<terminalDto>> getTerminalList()
        {
            return await sqlQuery.From<terminalEntity>("a").ToListAsync<terminalDto>();
        }


        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2022-10-25
        /// Description:  获取终端信息
        /// </summary>
        /// <param name="request">查询条件</param>
        /// <returns></returns>
        public async Task<terminalDto> getTerminalInfo(terminalRequest request)
        {
           return await sqlQuery.From<terminalEntity>("a").WhereIfNotEmpty<terminalEntity>(x => x.TERMINAL, request.terminal_id).ToAsync<terminalDto>();
        }

        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2022-10-25
        /// Description:  获取终端信息
        /// </summary>
        /// <param name="request">查询条件</param>
        /// <returns></returns>
        public async Task<terminalDto> getTerminalInfoByMac(string mac)
        {
            return await sqlQuery.From<terminalEntity>("a").AppendWhere($" MAC='{mac}' OR SECONDMAC='{mac}'").ToAsync<terminalDto>();
        }

        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2024-10-25
        /// Description:  获取门店信息
        /// </summary>
        /// <param name="request">查询条件</param>
        /// <returns></returns>
        public async Task<shopDto> getShopInfoById(string shop_id)
        {
            return await sqlQuery.From<shopEntity>("a").Where<shopEntity>(x => x.store_no, shop_id).ToAsync<shopDto>();
        }


    }
}
