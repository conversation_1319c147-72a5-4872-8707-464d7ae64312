﻿using AppService.Dtos.product;
using Domain.Models.product;

namespace AppService.Impl
{
    /// <summary>
    /// Author：Aarons
    /// Create date：2022-05-31
    /// Description：商品应用服务
    /// </summary>
    public class ProdcutAppService : AppServiceBase, IProdcutAppService
    {
        /// <summary>
        /// Sql查询对象
        /// </summary>
        protected ISqlQuery sqlQuery { get; set; }

        /// <summary>
        /// FreeSql查询对象
        /// </summary>
        private readonly IFreeSql _freeSql;

        /// <summary>
        /// 初始化应用服务
        /// </summary>
        public ProdcutAppService(ISqlQuery _sqlQuery, IFreeSql freeSql)
        {
            sqlQuery = _sqlQuery;
            _freeSql = freeSql;
        }


        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2022-05-31
        /// Description:  获取PLU商品
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns></returns>
        public async Task<PagerList<pluDto>> getPagePluList(pluQuery query)
        {
            string[] array = { query.shop_id, "-" };
            return await sqlQuery.From<pluEntity>("a")
                .Where<pluEntity>(x => x.TERMINALID, array, BPM.Operator.In)
                .ToPagerListAsync<pluDto>(new Pager(query.Page, query.PageSize, query.Order));
        }

       /// <summary>
       /// 商品信息
       /// </summary>
       /// <param name="plu_code"></param>
       /// <returns></returns>
        public async Task<pluDto> getPluInfo(string plu_code)
        {
            var entity = await _freeSql.Select<pluEntity>().Where(x => x.PLUCODE == plu_code).ToOneAsync();
            return entity.MapTo<pluDto>();
        }


        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2022-05-31
        /// Description:  获取品牌商品
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns></returns>
        public async Task<PagerList<brandDto>> getPageBrandList(baseQuery query)
        {
            return await sqlQuery.From<brandEntity>("a").ToPagerListAsync<brandDto>(new Pager(query.Page, query.PageSize, query.Order));
        }

        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2022-05-31
        /// Description:  获取分类商品
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns></returns>
        public async Task<PagerList<classDto>> getPageClassList(baseQuery query)
        {
            return await sqlQuery.From<dispayEntity>("a").ToPagerListAsync<classDto>(new Pager(query.Page, query.PageSize, query.Order));
        }


        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2022-05-31
        /// Description:  获取分类商品
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns></returns>
        public async Task<PagerList<displayDto>> getPageDisplayList(baseQuery query)
        {
            string[] array = { query.shop_id, "-" };
            return await sqlQuery.From<displayEntity>("a")
                 .Where<displayEntity>(x => x.TERMINALID, array, BPM.Operator.In)
                .ToPagerListAsync<displayDto>(query);
        }


        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2022-05-31
        /// Description:  获取部类商品
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns></returns>
        public async Task<PagerList<departmentDto>> getPageDepartmentList(baseQuery query)
        {
            return await sqlQuery.From<departmentEntity>("a").ToPagerListAsync<departmentDto>(new Pager(query.Page, query.PageSize, query.Order));
        }

        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2022-07-05
        /// Description:  获取代收
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns></returns>
        public async Task<List<raDto>> getPageRaList(baseFreeSqlQuery query)
        {
            return await _freeSql.Select<raEntity>().OrderByDescending(x => x.RACODE).Page(query).ToListAsync<raDto>();
            // await sqlQuery.From<raEntity>("a").ToPagerListAsync<raDto>(new Pager(query.PageNumber, query.PageSize, query.Order));
        }

        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2022-07-05
        /// Description:  获取库存
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns></returns>
        public async Task<PagerList<stockDto>> getPageStockList(stockQuery query)
        {
            return await sqlQuery.From<stockEntity>("a")
               .WhereIfNotEmpty<stockEntity>(p => p.WAREHOUSE, query.shop_id)
               .ToPagerListAsync<stockDto>(new Pager(query.Page, query.PageSize, query.Order));
        }

        /// <summary>
        /// 获取商品库商品
        /// </summary>
        /// <param name="product_id"></param>
        /// <returns></returns>
        public async Task<productSkuEntity> getProductSkuInfo(string sku_sn)
        {
            return await _freeSql.Select<productSkuEntity>().Where(x => x.sku_sn == sku_sn && x.channel == 1).ToOneAsync();
        }

        /// <summary>
        /// 获取网店商品库商品
        /// </summary>
        /// <param name="sku_sn">商品SKU编号</param>
        /// <returns>商品SKU信息</returns>
        public async Task<productSkuEntity> getProductSkuInfoByChannel0(string sku_sn)
        {
            return await _freeSql.Select<productSkuEntity>().Where(x => x.sku_sn == sku_sn && x.channel == 0).ToOneAsync();
        }

        /// <summary>
        /// 获取网店商品库商品（含条码）
        /// </summary>
        /// <param name="sku_sn">商品SKU编号</param>
        /// <returns>商品SKU信息（含条码）</returns>
        public async Task<ProductSkuWithBarcodeDto> getProductSkuInfoWithBarcodeByChannel0(string sku_sn)
        {
            var query = await _freeSql.Select<productSkuEntity, productEntity>()
                .LeftJoin((s, p) => s.product_id == p.product_id)
                .Where((s, p) => s.sku_sn == sku_sn && s.channel == 0)
                .ToOneAsync((s, p) => new ProductSkuWithBarcodeDto 
                {
                    id = s.id,
                    product_id = s.product_id,
                    sku_sn = s.sku_sn,
                    sku_name = s.sku_name,
                    make_price = s.make_price,
                    sale_price = s.sale_price,
                    cost_price = s.cost_price,
                    des = s.des,
                    group = s.group,
                    sku_state = s.sku_state,
                    channel = s.channel,
                    bar_code = p.bar_code,
                    has_sku = p.has_sku
                });
            return query;
        }

    }
}
