﻿using AppService.Dtos.user;
using Domain.Models.user;
using Domain.order.Repository;

namespace AppService.Impl
{
    /// <summary>
    /// Author：Aarons
    /// Create date：2022-05-31
    /// Description：用户应用服务
    /// </summary>
    public class UserAppService : AppServiceBase, IUserAppService
    {

        /// <summary>
        /// 订单仓储接口
        /// </summary>
        private readonly IOrderRepository _orderRepository;
        /// <summary>
        /// Sql查询对象
        /// </summary>
        protected ISqlQuery sqlQuery { get; set; }
        /// <summary>
        /// 工作单元
        /// </summary>
        private readonly IUnitOfWork _unitOfWork;
        /// <summary>
        /// sql 执行器
        /// </summary>
        protected ISqlExecutor _sqlExecutor;
        /// <summary>
        /// 初始化应用服务
        /// </summary>
        public UserAppService(ISqlQuery _sqlQuery, IOrderRepository orderRepository, ISqlExecutor sqlExecutor, IUnitOfWork unitOfWork)
        {
            sqlQuery = _sqlQuery;
            _orderRepository = orderRepository;
            _sqlExecutor = sqlExecutor;
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2022-05-31
        /// Description:  获取用户商品
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns></returns>
        public async Task<PagerList<userDto>> getPageUserList(baseQuery query)
        {
            return await sqlQuery.From<userEntity>("a").ToPagerListAsync<userDto>(new Pager(query.Page, query.PageSize, query.Order));
        }

        /// <summary>
        ///  用户操作日志
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns></returns>
        public async Task<int> saveUserLogs(userLogsRequest request)
        {
            await _sqlExecutor.ExecuteSqlAsync("DELETE UserLog_tab WHERE TERMINAL=@TERMINAL AND CONVERT(VARCHAR(20),LogDate,23)=@LogDate", new { TERMINAL = request.TERMINAL, LogDate = request.LogDate });
            var items = request.items.ToObject<List<userLogsEntity>>();
            //插入订单
            await _orderRepository.saveUserLogs(items);
            //工作单元
            await _unitOfWork.CommitAsync();
            return items.Count;
        }



    }
}
