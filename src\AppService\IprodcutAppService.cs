﻿using AppService.Dtos.product;
using Domain.Models.product;

namespace AppService
{
    /// <summary>
    /// Author：Aarons
    /// Create date：2022-05-31
    /// Description：商品应用服务接口
    /// </summary>
    public interface IProdcutAppService
    {
        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2022-05-31
        /// Description:  获取PLU商品
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns></returns>
        Task<PagerList<pluDto>> getPagePluList(pluQuery query);

        /// <summary>
        /// 商品信息
        /// </summary>
        /// <param name="plu_code"></param>
        /// <returns></returns>
        Task<pluDto> getPluInfo(string plu_code);

        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2022-05-31
        /// Description:  获取品牌商品
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns></returns>
        Task<PagerList<brandDto>> getPageBrandList(baseQuery query);

        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2022-05-31
        /// Description:  获取分类商品
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns></returns>
        Task<PagerList<classDto>> getPageClassList(baseQuery query);
        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2022-05-31
        /// Description:  获取分类商品
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns></returns>
        Task<PagerList<displayDto>> getPageDisplayList(baseQuery query);

        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2022-05-31
        /// Description:  获取部类商品
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns></returns>
        Task<PagerList<departmentDto>> getPageDepartmentList(baseQuery query);

        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2022-07-05
        /// Description:  获取代收
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns></returns>
        Task<List<raDto>> getPageRaList(baseFreeSqlQuery query);

        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2022-07-05
        /// Description:  获取库存
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns></returns>
        Task<PagerList<stockDto>> getPageStockList(stockQuery query);

        /// <summary>
        /// 获取商品库商品
        /// </summary>
        /// <param name="product_id"></param>
        /// <returns></returns>
        Task<productSkuEntity> getProductSkuInfo(string sku_sn);

        /// <summary>
        /// 获取网店商品库商品
        /// </summary>
        /// <param name="sku_sn">商品SKU编号</param>
        /// <returns>商品SKU信息</returns>
        Task<productSkuEntity> getProductSkuInfoByChannel0(string sku_sn);

        /// <summary>
        /// 获取网店商品库商品（含条码）
        /// </summary>
        /// <param name="sku_sn">商品SKU编号</param>
        /// <returns>商品SKU信息（含条码）</returns>
        Task<ProductSkuWithBarcodeDto> getProductSkuInfoWithBarcodeByChannel0(string sku_sn);
    }
}
