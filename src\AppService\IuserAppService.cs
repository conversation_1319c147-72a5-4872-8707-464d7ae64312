﻿using AppService.Dtos.user;

namespace AppService
{
    /// <summary>
    /// Author：Aarons
    /// Create date：2022-05-31
    /// Description： 用户应用服务接口
    /// </summary>
    public interface IUserAppService
    {
        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2022-05-31
        /// Description:  获取用户商品
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns></returns>
        Task<PagerList<userDto>> getPageUserList(baseQuery query);
        /// <summary>
        ///  用户操作日志
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns></returns>
        Task<int> saveUserLogs(userLogsRequest request);
    }
}
