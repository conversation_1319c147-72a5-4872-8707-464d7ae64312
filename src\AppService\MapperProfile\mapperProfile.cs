﻿using AutoMapper;
using Domain.Models.card;

namespace BPM.AppService.MapperProfile
{
    public class mapperProfile : Profile, IObjectMapperProfile
    {

        public mapperProfile()
        {
            CreateMap<cardTxnsRequest, cardTxnsEntity>()
               .ForMember(d => d.SHOP, opt => opt.MapFrom(s => s.shop_id))
               .ForMember(d => d.TERMINALID, opt => opt.MapFrom(s => s.terminal_id))
               .ForMember(d => d.CARDNUM, opt => opt.MapFrom(s => s.card_code))
               .ForMember(d => d.OPERATOR, opt => opt.MapFrom(s => s.operator_id))
               .ForMember(d => d.DEBAMT, opt => opt.MapFrom(s => s.trade_fee.ToDecimal()));


            CreateMap<cardRechargeRequest, cardEntity>()
              .ForMember(d => d.WAREHOUSE, opt => opt.MapFrom(s => s.shop_id))
              .ForMember(d => d.FACE, opt => opt.MapFrom(s => s.face_fee))
              .ForMember(d => d.BALANCE, opt => opt.MapFrom(s => s.face_fee));


            CreateMap<cardRechargeRequest, cardAdmTxnsEntity>()
            .ForMember(d => d.TERMINALID, opt => opt.MapFrom(s => s.terminal_id))
            .ForMember(d => d.CARDNUM, opt => opt.MapFrom(s => s.card_code))
            .ForMember(d => d.ORGVALUE, opt => opt.MapFrom(s => s.face_fee))
            .ForMember(d => d.OPVALUE, opt => opt.MapFrom(s => s.trade_fee))
            .ForMember(d => d.SERIALNO, opt => opt.MapFrom(s => s.txnnum)) ;
        }

        public void CreateMap()
        {

        }
    }
}
