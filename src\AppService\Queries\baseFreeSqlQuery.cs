﻿namespace AppService.Queries;

/// <summary>
/// 基础Query
/// </summary>
public class baseFreeSqlQuery : freeSqlPager
{

    public baseFreeSqlQuery() { }

    /// <summary>
    /// 终端号
    /// </summary>
    public string terminal_id { get; set; }

    /// <summary>
    /// 仓号
    /// </summary>
    public string shop_id { get; set; }

    public baseFreeSqlQuery(int Page, int PageSize, string Order)
    {
        this.PageNumber = Page;
        this.PageSize = PageSize;
        this.Order = Order;
    }


}

