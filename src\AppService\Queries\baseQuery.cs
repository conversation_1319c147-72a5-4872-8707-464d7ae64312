﻿using BPM.Data.Queries;

namespace AppService.Queries;

/// <summary>
/// 基础Query
/// </summary>
public class baseQuery : QueryParameter
{

    public baseQuery() { }

    /// <summary>
    /// 终端号
    /// </summary>
    public string terminal_id { get; set; }

    /// <summary>
    /// 仓号
    /// </summary>
    public string shop_id { get; set; }

    public baseQuery(int Page, int PageSize, string Order)
    {
        this.Page = Page;
        this.PageSize = PageSize;
        this.Order = Order;
    }


}

