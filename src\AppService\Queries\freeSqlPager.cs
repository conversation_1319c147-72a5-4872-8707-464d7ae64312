﻿using FreeSql.Internal.Model;

namespace AppService.Queries;

/// <summary>
/// 扩展FreeSql分页
/// </summary>
public class freeSqlPager : BasePagingInfo
{
    /// <summary>
    /// 排序条件
    /// </summary>
    public string Order { get; set; }

    /// <summary>
    /// 获取总页数
    /// </summary>
    public long GetPageCount()
    {
        if (Count % PageSize == 0)
            return Count / PageSize;
        return (Count / PageSize) + 1;
    }
}

