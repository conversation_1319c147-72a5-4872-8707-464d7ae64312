﻿using System.ComponentModel.DataAnnotations;

namespace AppService.Requests
{
    /// <summary>
    /// 账号登录请求
    /// </summary>
    public class accessTokenRequest
    {
        /// <summary>
        /// 应用编号
        /// </summary>
        [Required(ErrorMessage = "应用编号[app_id]不能为空")]
        public string app_id { get; set; }
        /// <summary>
        /// 随机数
        /// </summary>
        [Required(ErrorMessage = "随机数[nonce_str]不能为空")]
        public string nonce_str { get; set; }

        /// <summary>
        /// 随机数
        /// </summary>
        [Required(ErrorMessage = "Mac地址不能为空")]
        public string mac { get; set; }

        /// <summary>
        /// 时间戳
        /// </summary>
        [Required(ErrorMessage = "时间戳[time_stamp]不能为空")]
        public string time_stamp { get; set; }
        /// <summary>
        /// 签名
        /// </summary>
        [Required(ErrorMessage = "签名[sign]不能为空")]
        public string sign { get; set; }

    }

}
