﻿using System.ComponentModel.DataAnnotations;
namespace AppService.Requests;

/// <summary>
/// 基类请求
/// </summary>
public class baseRequest : signRequestBase
{
    /// <summary>
    ///  分页索引
    /// </summary>
    [Required(ErrorMessage = "分页索引[page_index]不能为空")]
    public int page_index { get; set; }

    /// <summary>
    ///  分页行数
    /// </summary>
    [Required(ErrorMessage = "分页行数[page_index]不能为空")]
    public int page_size { get; set; }
}

