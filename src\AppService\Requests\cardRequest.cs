﻿using System.ComponentModel.DataAnnotations;

namespace AppService.Requests
{
    /// <summary>
    /// 订单传输dto
    /// </summary>
    public class cardQueryRequest : signRequestBase
    {
        /// <summary>
        /// 卡号
        /// </summary>
        [Required(ErrorMessage = "卡号[card_code]不能为空")]
        public string card_code { get; set; }

    }

    /// <summary>
    /// 储值卡面值
    /// </summary>
    public class cardFaceRequest : baseRequest
    {
        /// <summary>
        ///  门店编号
        /// </summary>
        [Required(ErrorMessage = "门店编号[shop_id]不能为空")]
        public string shop_id { get; set; }
    }

    /// <summary>
    ///  校验请求
    /// </summary>
    public class cardvfRequest : cardQueryRequest
    {
        /// <summary>
        /// 校验码
        /// </summary>
        [Required(ErrorMessage = "校验码[vf_code]不能为空")]
        public string vf_code { get; set; }


    }
 


    /// <summary>
    /// 储值卡消费传输dto
    /// </summary>
    public class cardTxnsRequest : signRequestBase
    {
        /// <summary>
        /// 卡号
        /// </summary>
        [Required(ErrorMessage = "卡号[card_code]不能为空")]
        public string card_code { get; set; }
        /// <summary>
        /// 交易金额
        /// </summary>
        [Required(ErrorMessage = "交易金额[trade_fee]不能为空")]
        public string trade_fee { get; set; }
        /// <summary>
        /// 交易单号
        /// </summary>
        [Required(ErrorMessage = "交易单号[txnnum]不能为空")]
        public string txnnum { get; set; }
        /// <summary>
        /// 交易编号
        /// </summary>
        [Required(ErrorMessage = "交易编号[payment_sn]不能为空")]
        public string payment_sn { get; set; }
        /// <summary>
        /// 门店编号
        /// </summary>
        [Required(ErrorMessage = "门店编号[shop_id]不能为空")]
        public string shop_id { get; set; }
        /// <summary>
        /// 终端编号
        /// </summary>
        [Required(ErrorMessage = "终端编号[terminalid]不能为空")]
        public string terminal_id { get; set; }
        /// <summary>
        /// 操作员编号
        /// </summary>
        [Required(ErrorMessage = "操作员编号[operator_id]不能为空")]
        public string operator_id { get; set; }
        /// <summary>
        /// 订单序号
        /// </summary>
        [Required(ErrorMessage = "订单序号[vnum]不能为空")]
        public int vnum { get; set; }
    }

    /// <summary>
    /// 储值卡充值
    /// </summary>
    public class cardRechargeRequest : cardvfRequest
    {
        /// <summary>
        /// 交易单号
        /// </summary>
        [Required(ErrorMessage = "交易单号[txnnum]不能为空")]
        public string txnnum { get; set; }
        /// <summary>
        /// 门店编号
        /// </summary>
        [Required(ErrorMessage = "门店编号[shop_id]不能为空")]
        public string shop_id { get; set; }

        /// <summary>
        /// 终端编号
        /// </summary>
        [Required(ErrorMessage = "终端编号[terminalid]不能为空")]
        public string terminal_id { get; set; }
        /// <summary>
        /// 操作员编号
        /// </summary>
        [Required(ErrorMessage = "操作员编号[operator_id]不能为空")]
        public string operator_id { get; set; }

        /// <summary>
        /// 面值金额
        /// </summary>
        [Required(ErrorMessage = "面值金额[face_fee]不能为空")]
        public string face_fee { get; set; }

        /// <summary>
        /// 实际金额
        /// </summary>
        [Required(ErrorMessage = "实际金额[trade_fee]不能为空")]
        public string trade_fee { get; set; }




    }
}
