﻿using System.ComponentModel.DataAnnotations;

namespace AppService.Requests
{

    /// <summary>
    /// 优惠券请求
    /// </summary>
    public class couponOrderListRequest : signRequestBase
    {
        /// <summary>
        /// 订单号
        /// </summary>
        public string orderNo { get; set; }
    }

    /// <summary>
    /// 优惠券请求
    /// </summary>
    public class couponListRequest : signRequestBase
    {
        /// <summary>
        /// openId
        /// </summary>
        [Required(ErrorMessage = "openId[openId]不能为空")]
        public string openId { get; set; }

        /// <summary>
        /// 门店编号
        /// </summary>
        [Required(ErrorMessage = "门店编号[shopId]不能为空")]
        public string shopId { get; set; }

        /// <summary>
        /// 商品明细
        /// </summary>
        public string items { get; set; }

    }

    /// <summary>
    /// 撤销券核销
    /// </summary>
    public class couponCancelRequest : signRequestBase
    {

        /// <summary>
        /// 门店编号
        /// </summary>
        [Required(ErrorMessage = "门店编号[shopId]不能为空")]
        public string shopId { get; set; }

        /// <summary>
        /// 优惠券id
        /// </summary>
        [Required(ErrorMessage = "优惠券id[couponId]不能为空")]
        public string couponId { get; set; }


        /// <summary>
        /// 订单编号
        /// </summary>
        [Required(ErrorMessage = "订单[orderNo]不能为空")]
        public string orderNo { get; set; }
    }

    /// <summary>
    /// 查询优惠券
    /// </summary>
    public class couponQueryRequest : signRequestBase
    {
        /// <summary>
        /// 门店编号
        /// </summary>
        [Required(ErrorMessage = "门店编号[shopId]不能为空")]
        public string shopId { get; set; }

        /// <summary>
        /// 优惠券id
        /// </summary>
        [Required(ErrorMessage = "优惠券id[couponId]不能为空")]
        public string couponId { get; set; }
    }

    /// <summary>
    /// 优惠券核销
    /// </summary>
    public class couponVerifyRequest : signRequestBase
    {

        /// <summary>
        /// 门店编号
        /// </summary>
        [Required(ErrorMessage = "门店编号[shopId]不能为空")]
        public string shopId { get; set; }

        /// <summary>
        /// 订单编号
        /// </summary>
        [Required(ErrorMessage = "订单[orderNo]不能为空")]
        public string orderNo { get; set; }

        /// <summary>
        /// 券在订单上的优惠金额，单位:分
        /// </summary>
        [Required(ErrorMessage = "订单[amount]不能为空")]
        public long amount { get; set; }

        /// <summary>
        /// 优惠券集合
        /// </summary>
        [Required(ErrorMessage = "优惠券[couponNoList]不能为空,格式124,234")]
        public string couponNoList { get; set; }

    }


    /// <summary>
    /// 优惠券验核
    /// </summary>
    public class couponCheckVerifyRequest : signRequestBase
    {
        /// <summary>
        /// openId
        /// </summary>
        [Required(ErrorMessage = "openId[openId]不能为空")]
        public string openId { get; set; }

        /// <summary>
        /// 门店编号
        /// </summary>
        [Required(ErrorMessage = "门店编号[shopId]不能为空")]
        public string shopId { get; set; }

        /// <summary>
        /// 优惠券集合
        /// </summary>
        [Required(ErrorMessage = "优惠券[couponNoList]不能为空,格式124,234")]
        public string couponNoList { get; set; }

        /// <summary>
        /// 商品明细
        /// </summary>
        public string items { get; set; }
    }

    /// <summary>
    /// 订单明细
    /// </summary>
    public class orderItemList
    {
        /// <summary>
        ///  外部商品编号
        /// </summary>
        public string outerProductCode { get; set; }

        /// <summary>
        /// 外部门店编号
        /// </summary>
        public string outerShopCode { get; set; }

        /// <summary>
        /// 参与优惠券计算的商品总价，单位元，保留到小数点后2位
        /// </summary>
        public string promoPrice { get; set; }

        /// <summary>
        /// 销售价
        /// </summary>
        public string salePrice { get; set; }

        /// <summary>
        /// 销售数量
        /// </summary>
        public string saleQty { get; set; }

        /// <summary>
        /// 商品行号
        /// </summary>
        public string orderItemNo { get; set; }

        /// <summary>
        ///是否排除用券计算 0-否，1-是；不传默认0
        /// </summary>
        public int excludeFlag { get; set; } = 0;



    }

    /// <summary>
    /// 优惠券响应对象
    /// </summary>
    public class couponListResponse
    {
        /// <summary>
        /// 最优推荐组合总的优惠金额
        /// </summary>
        public string totalCouponDiscount { get; set; }

        /// <summary>
        /// 最优推荐组合总的优惠券购券金额
        /// </summary>
        public string totalCouponOrderAmount { get; set; }

        /// <summary>
        /// 可用优惠券
        /// </summary>
        public List<CouponList> availableCouponList { get; set; }

        /// <summary>
        /// 不可用优惠券
        /// </summary>
        public List<CouponList> notAvailableCouponList { get; set; }
    }

    /// <summary>
    /// 优惠券列表
    /// </summary>
    public class CouponList
    {
        /// <summary>
        /// 优惠券券码
        /// </summary>
        public string couponNo { get; set; }
        /// <summary>
        /// 优惠总金额，单位元，保留到小数点后2位
        /// </summary>
        public decimal actualDiscount { get; set; }

        /// <summary>
        /// 优惠券类型：1-满减券;2-兑换券;3-运费券
        /// </summary>
        public int couponType { get; set; }

        /// <summary>
        /// 优惠券面额，，单位元，保留到小数点后2位
        /// </summary>
        public decimal discount { get; set; }

        /// <summary>
        /// 优惠券名称
        /// </summary>
        public string name { get; set; }

        /// <summary>
        ///  描述
        /// </summary>
        public string description { get; set; }

        /// <summary>
        /// 使用有效期开始时间
        /// </summary>
        public string useStartTime { get; set; }

        /// <summary>
        /// 使用有效期结束时间
        /// </summary>
        public string useEndTime { get; set; }

        /// <summary>
        /// 券使用门槛
        /// </summary>
        public string useThreshold { get; set; }

        /// <summary>
        /// 获券方式：1-免费领取；2-积分兑换；
        /// </summary>
        public string getWay { get; set; }
    }

}
