﻿using System.ComponentModel.DataAnnotations;

namespace AppService.Requests
{
    public class memberRequest : baseRequest
    {
        /// <summary>
        ///  手机号/卡号
        /// </summary>
        [Required(ErrorMessage = "手机号/卡号[code]不能为空")]
        public string code { get; set; }
    }

    public class memberCodeRequest : signRequestBase
    {
        /// <summary>
        ///  手机号/卡号
        /// </summary>
        [Required(ErrorMessage = "会员二维码[code]不能为空")]
        public string code { get; set; }

        /// <summary>
        ///  操作人
        /// </summary>
        [Required(ErrorMessage = "操作人[operator]不能为空")]
        public string @operator { get; set; }

        /// <summary>
        ///  mac
        /// </summary>
        [Required(ErrorMessage = "[mac]不能为空")]
        public string mac { get; set; }

        /// <summary>
        ///  门店编号
        /// </summary>
        [Required(ErrorMessage = "仓号[shop_id]不能为空")]
        public string shop_id { get; set; }

    }

}
