﻿using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using System.ComponentModel.DataAnnotations;

namespace AppService.Requests
{
    /// <summary>
    /// 订单传输dto
    /// </summary>
    public class orderRequest: signRequestBase
    {
        /// <summary>
        /// 单号
        /// </summary>
        [Required(ErrorMessage = "单号[TXNNUM]不能为空")]
        public string TXNNUM { get; set; }

        /// <summary>
        /// 是否是会员
        /// </summary>
        public bool is_member { get; set; } = false;

        /// <summary>
        ///  是否销售
        /// </summary>
        public bool is_sale { get; set; } = true;

        /// <summary>
        /// openId
        /// </summary>
        public string open_id { get; set; }

        /// <summary>
        /// 订单明细
        /// </summary>
        [Required(ErrorMessage = "交易明细[items]不能为空")]
        public string items { get; set; }

    }

    /// <summary>
    /// 订单清机请求
    /// </summary>
    public class orderPurgRequest : signRequestBase
    {
        /// <summary>
        /// 机号
        /// </summary>
        [Required(ErrorMessage = "机号[TERMINAL]不能为空")]
        public string TERMINAL { get; set; }
        /// <summary>
        /// 交易日期
        /// </summary>
        [Required(ErrorMessage = "交易日期[TXNTIME]不能为空")]
        public string TXNTIME { get; set; }
        /// <summary>
        /// 订单明细
        /// </summary>
        [Required(ErrorMessage = "交易明细[items]不能为空")]
        public string items { get; set; }
    }


    /// <summary>
    /// 订单同步请求
    /// </summary>
    public class orderSyncRequest : signRequestBase
    {
        /// <summary>
        /// 仓号
        /// </summary>
        [Required(ErrorMessage = "仓号[SHOPID]不能为空")]
        public string SHOPID { get; set; }

        /// <summary>
        /// 机号
        /// </summary>
        [Required(ErrorMessage = "机号[TERMINAL]不能为空")]
        public string TERMINAL { get; set; }
        /// <summary>
        /// 交易日期
        /// </summary>
        [Required(ErrorMessage = "交易日期[TXNTIME](yyyy-MM-dd)不能为空")]
        public string TXNTIME { get; set; }

    }



    public class orderItem
    {

        /// <summary>
        /// 单号
        /// </summary>
        public string TXNID { get; set; }

        /// <summary>
        /// 单号
        /// </summary>
        public string TXNNUM { get; set; }

        /// <summary>
        /// 仓号
        /// </summary>
        public string SHOPID { get; set; }

        /// <summary>
        /// 机号
        /// </summary>
        public string TERMINAL { get; set; }

        /// <summary>
        /// 班次号
        /// </summary>
        public string SHIFT { get; set; }

        /// <summary>
        /// 操作员
        /// </summary>
        public string OPERATOR { get; set; }

        /// <summary>
        /// 交易时间
        /// </summary>
        public DateTime TXNTIME { get; set; }

        /// <summary>
        /// 单号
        /// </summary>
        public int VOUCHNUM { get; set; }

        /// <summary>
        /// 序号
        /// </summary>
        public int SEQUENCE { get; set; }

        /// <summary>
        /// 交易类型 0- 支付，1-退款
        /// </summary>
        public int TXNTYPE { get; set; }

        /// <summary>
        /// 销售类型
        /// </summary>
        public int SALESTYPE { get; set; }

        /// <summary>
        /// 部类号
        /// </summary>
        public string DPTCODE { get; set; }

        /// <summary>
        /// 营销分类号
        /// </summary>
        public string CLSCODE { get; set; }

        /// <summary>
        /// 陈列分类
        /// </summary>
        public string DISPLAY { get; set; }

        /// <summary>
        /// 品牌
        /// </summary>
        public string BRAND { get; set; }

        /// <summary>
        /// 代码
        /// </summary>
        public string CODE { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public int QTY { get; set; }

        /// <summary>
        /// 单价
        /// </summary>
        public int PRICE { get; set; }
        /// <summary>
        /// 折扣
        /// </summary>
        public int DISC { get; set; }
        /// <summary>
        /// 码洋
        /// </summary>
        public int ORGAMT { get; set; }

        /// <summary>
        /// 明细折扣额
        /// </summary>
        public int ITEMDISC { get; set; }

        /// <summary>
        /// 整单折扣
        /// </summary>
        public int TTLDISC { get; set; }

        public int REAL { get; set; }

        /// <summary>
        /// 会员号
        /// </summary>
        public string MEMBER { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        public int INCTAX { get; set; }

        public string SALES { get; set; }

        public string DISCREM { get; set; }

        /// <summary>
        /// 活动代码
        /// </summary>
        public string PROCODE { get; set; }

        /// <summary>
        /// 折扣分摊
        /// </summary>
        public int DISCOUNT { get; set; }

        /// <summary>
        /// 单据类型
        /// </summary>
        public string ORDERTYPE { get; set; }

        /// <summary>
        /// 手机号
        /// </summary>
        public string PHONE { get; set; }

        /// <summary>
        /// 积分倍率
        /// </summary>
        public int CPTS { get; set; }

    }
}
