﻿using System.ComponentModel.DataAnnotations;

namespace AppService.Requests
{

    /// <summary>
    /// 支付请求
    /// </summary>
    public class netPaymentRequset : signRequestBase
    {
        /// <summary>
        /// 订单编号
        /// </summary>
        [Required(ErrorMessage = "订单编号[order_sn]不能为空")]
        public string order_sn { get; set; }
        /// <summary>
        /// 支付交易码
        /// </summary>
        [Required(ErrorMessage = "支付交易码[auth_code]不能为空")]
        public string auth_code { get; set; }
        /// <summary>
        /// 支付单号
        /// </summary>
        [Required(ErrorMessage = "支付单号[payment_sn]不能为空")]
        public string payment_sn { get; set; }
        /// <summary>
        /// 交易金额
        /// </summary>
        [Required(ErrorMessage = "交易金额[trade_fee]不能为空")]
        public string trade_fee { get; set; }
        /// <summary>
        /// 门店编号
        /// </summary>
        [Required(ErrorMessage = "门店编号[shop_id]不能为空")]
        public string shop_id { get; set; }
        /// <summary>
        /// 门店名称
        /// </summary>
        [Required(ErrorMessage = "门店编号[shop_name]不能为空")]
        public string shop_name { get; set; }
        /// <summary>
        /// 描述
        /// </summary>
        [Required(ErrorMessage = "描述[attach]不能为空")]
        public string attach { get; set; }
        /// <summary>
        /// 设备号
        /// </summary>
        [Required(ErrorMessage = "设备号[device_id]不能为空")]
        public string device_id { get; set; }

        /// <summary>
        /// 操作员编号
        /// </summary>
        [Required(ErrorMessage = "操作员编号[operator_id]不能为空")]
        public string operator_id { get; set; }
        
    }
    /// <summary>
    /// 退款请求
    /// </summary>
    public class netRefundRequset : signRequestBase
    {
        /// <summary>
        /// 门店编号
        /// </summary>
        [Required(ErrorMessage = "门店编号[shop_id]不能为空")]
        public string shop_id { get; set; }
        /// <summary>
        /// 设备号
        /// </summary>
        [Required(ErrorMessage = "设备号[device_id]不能为空")]
        public string device_id { get; set; }
        /// <summary>
        /// 订单编号
        /// </summary>
        public string order_sn { get; set; }

        /// <summary>
        /// 交易单号
        /// </summary>
        [Required(ErrorMessage = "支付单号[payment_sn]不能为空")]
        public string payment_sn { get; set; }

        /// <summary>
        /// 退款单号
        /// </summary>
        [Required(ErrorMessage = "退款单号[refund_sn]不能为空")]
        public string refund_sn { get; set; }
        /// <summary>
        /// 退款金额
        /// </summary>
        [Required(ErrorMessage = "退款金额[refund_fee]不能为空")]
        public string refund_fee { get; set; }
     
    }

    /// <summary>
    /// 支付查询请求
    /// </summary>
    public class netQueryRequset : signRequestBase
    {
        /// <summary>
        /// 门店编号
        /// </summary>
        [Required(ErrorMessage = "门店编号[shop_id]不能为空")]
        public string shop_id { get; set; }
        /// <summary>
        /// 交易单号
        /// </summary>
        [Required(ErrorMessage = "支付单号[payment_sn]不能为空")]
        public string payment_sn { get; set; }

    }

}
