﻿using System.ComponentModel.DataAnnotations;

namespace AppService.Requests
{
    public class pluRequest : signRequestBase
    {

        /// <summary>
        ///  分页索引
        /// </summary>
        [Required(ErrorMessage = "分页索引[page_index]不能为空")]
        public int page_index { get; set; }

        /// <summary>
        ///  分页行数
        /// </summary>
        [Required(ErrorMessage = "分页行数[page_index]不能为空")]
        public int page_size { get; set; }

        /// <summary>
        ///  仓号
        /// </summary>
        [Required(ErrorMessage = "仓号[shop_id]不能为空")]
        public string shop_id { get; set; }

    }
}
