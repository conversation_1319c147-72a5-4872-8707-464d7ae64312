﻿using System.ComponentModel.DataAnnotations;

namespace AppService.Requests
{
    public class promotionsRequest : baseRequest
    {

        /// <summary>
        ///  终端机号
        /// </summary>
        [Required(ErrorMessage = "终端编号[terminal_id]不能为空")]
        public string terminal_id { get; set; }
    }

    /// <summary>
    /// 满赠请求
    /// </summary>
    public class gifthdrRequest : baseRequest
    {

        /// <summary>
        ///  门店编号
        /// </summary>
        [Required(ErrorMessage = "门店编号[shop_id]不能为空")]
        public string shop_id { get; set; }
    }


    /// <summary>
    /// 换购请求
    /// </summary>
    public class tradeItemsRequest : baseRequest
    {

        /// <summary>
        ///  门店编号
        /// </summary>
        [Required(ErrorMessage = "门店编号[shop_id]不能为空")]
        public string shop_id { get; set; }
    }


}
