﻿using System.ComponentModel.DataAnnotations;

namespace AppService.Requests
{
    /// <summary>
    /// 订单清机请求
    /// </summary>
    public class userLogsRequest : signRequestBase
    {
        /// <summary>
        /// 机号
        /// </summary>
        [Required(ErrorMessage = "机号[TERMINAL]不能为空")]
        public string TERMINAL { get; set; }
        /// <summary>
        /// 交易日期
        /// </summary>
        [Required(ErrorMessage = "日志日期[LogDate]不能为空")]
        public string LogDate { get; set; }
        /// <summary>
        /// 订单明细
        /// </summary>
        [Required(ErrorMessage = "交易明细[items]不能为空")]
        public string items { get; set; }
    }


    /// <summary>
    /// 用户日志dto
    /// </summary>
    public class userLogsItems
    {
        /// <summary>
        /// 日期
        /// </summary>
        public DateTime LogDate { get; set; }

       /// <summary>
       /// 时间
       /// </summary>
        public string LogTime { get; set; }
        /// <summary>
        /// 电脑名称
        /// </summary>
        public string ComputerName { get; set; }
        /// <summary>
        /// ip 地址
        /// </summary>
        public string IPADDRESS { get; set; }
        /// <summary>
        /// 机号
        /// </summary>
        public string TERMINAL { get; set; }
        /// <summary>
        /// 功能
        /// </summary>
        public string FUNCNAME { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string REMARKS { get; set; }

        /// <summary>
        /// 用户代码
        /// </summary>
        public string UserCode { get; set; }

        /// <summary>
        /// 用户名称
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public short STS { get; set; }
    }
}
