﻿using System.ComponentModel.DataAnnotations;

namespace AppService.Requests;


/// <summary>
/// 电子钱包基类
/// </summary>
public class walletBase : signRequestBase
{
    /// <summary>
    ///  操作人
    /// </summary>
    [Required(ErrorMessage = "操作人[operator]不能为空")]
    public string @operator { get; set; }
    /// <summary>
    ///  mac
    /// </summary>
    [Required(ErrorMessage = "[mac]不能为空")]
    public string mac { get; set; }    

    /// <summary>
    ///  门店编号
    /// </summary>
    [Required(ErrorMessage = "仓号[shopCode]不能为空")]
    public string shopCode { get; set; }

}

/// <summary>
/// 电子钱包交易查询请求
/// </summary>
public class walletTradeQueryRequest: walletBase
{
    /// <summary>
    ///  交易类型(1=交易，2=退款)
    /// </summary>
    [Required(ErrorMessage = "[tradeType]不能为空")]
    public int tradeType { get; set; }

    /// <summary>
    /// 商户交易单号，商户内保证唯一
    /// </summary>
    public string outTradeNo { get; set; }

    /// <summary>
    /// 商户退款交易单号，商户内保证唯一
    /// </summary>
    public string outRefundNo { get; set; }
}

/// <summary>
/// 电子钱包消费请求
/// </summary>
public class walletTradeRequest : walletBase
{

    /// <summary>
    /// 扫描二维码获取到的内容
    /// </summary>
    [Required(ErrorMessage = "二维码[payCode]不能为空")]
    public string payCode { get; set; }

    /// <summary>
    ///  1-查询会员 2-钱包支付 默认为1
    /// </summary>
    [Required(ErrorMessage = "操作类型[operateType]不能为空")]
    public int operateType { get; set; }

    /// <summary>
    /// 交易金额，单位分 operateType 为2时必传
    /// </summary>
    [Required(ErrorMessage = "交易金额[amount]不能为空")]
    public int amount { get; set; }

    /// <summary>
    /// 商户交易单号，商户内保证唯一 ，operateType 为2 时必传
    /// </summary>
    [Required(ErrorMessage = "交易单号[outTradeNo]不能为空")]
    public string outTradeNo { get; set; }

}

/// <summary>
/// 电子钱包退款请求
/// </summary>
public class walletRefundRequest : walletBase
{
    /// <summary>
    /// 交易金额，单位分 operateType 为2时必传
    /// </summary>
    [Required(ErrorMessage = "交易金额[amount]不能为空")]
    public int amount { get; set; }

    /// <summary>
    /// 商户交易单号，商户内保证唯一 ，operateType 为2 时必传
    /// </summary>
    [Required(ErrorMessage = "交易单号[outTradeNo]不能为空")]
    public string outTradeNo { get; set; }

    /// <summary>
    /// 商户交易单号，商户内保证唯一 ，operateType 为2 时必传
    /// </summary>
    [Required(ErrorMessage = "退款交易单号[outRefundNo]不能为空")]
    public string outRefundNo { get; set; }

}
