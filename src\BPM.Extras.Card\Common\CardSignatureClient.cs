﻿using System.Security.Cryptography;
using System.Text;
using System.Text.Json;

namespace BPM.Extras.Card.Common;

/// <summary>
/// 签名类
/// </summary>
public class CardSignatureClient
{
    /// <summary>
    /// 生成签名的逻辑
    /// </summary>
    /// <param name="parameters">签名参数</param>
    /// <param name="signKey"></param>
    /// <param name="timestamp"></param>
    /// <returns></returns>
    public static string GenerateSignature(string parameters, string signKey, string timestamp)
    {
        var param = JsonSerializer.Deserialize<Dictionary<string, object>>(parameters);
        param.Add("timestamp", timestamp);
        var sortedParams = param.OrderBy(p => p.Key)
            .Select(p => $"{p.Key}={(p.Value is string || p.Value is JsonElement ? p.Value : JsonSerializer.Serialize(p.Value))}")
            .ToArray();

        var sortedString = string.Join("&", sortedParams);
        var dataToSign = sortedString + signKey;

        return Md5By32(dataToSign).ToUpper();
    }

    // MD5 32位加密算法
    private static string Md5By32(string input)
    {
        using (var md5 = MD5.Create())
        {
            var inputBytes = Encoding.UTF8.GetBytes(input);
            var hashBytes = md5.ComputeHash(inputBytes);
            return string.Concat(hashBytes.Select(b => b.ToString("x2")));
        }
    }

    /// <summary>
    /// HTTP POST 请求方法
    /// </summary>
    /// <param name="url">请求地址</param>
    /// <param name="parameters">请求</param>
    /// <param name="signKey">签名</param>
    /// <returns></returns>
    public static async Task<string> PostRequest(string url, Dictionary<string, object> dict, string signKey, string token)
    {
        string timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString();
        string sign = GenerateSignature(JsonSerializer.Serialize(dict), signKey, timestamp);
        // 将签名和时间戳加入到URL查询参数中
        string urlWithParams = $"{url}?timestamp={timestamp}&sign={sign}";
        return await BPM.Helpers.Web.Client().Post(urlWithParams).BearerToken(token).JsonData(dict).ResultAsync();
    }
}
