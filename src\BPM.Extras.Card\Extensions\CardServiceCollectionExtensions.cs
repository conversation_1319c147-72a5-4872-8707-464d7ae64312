﻿using BPM.Extras.Card.Options;
using BPM.Extras.Card.Services;

namespace BPM.Extras.Card.Extensions;

/// <summary>
/// 储值卡服务扩展类
/// </summary>
public static class CardServiceCollectionExtensions
{
    /// <summary>
    /// 添加有赞服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns></returns>
    public static IServiceCollection AddCard(this IServiceCollection services)
    {
        ConfigureCardOptions(services);
        services.AddTransient<ICardService, CardService>();
        return services;
    }

    /// <summary>
    /// 添加有赞配置
    /// </summary>
    /// <param name="services"></param>
    private static void ConfigureCardOptions(IServiceCollection services)
    {
        var configuration = services.GetConfiguration();
        var options = configuration.GetSection(nameof(CardOptions)).Value;
        // 配置验证
        services.AddOptions<CardOptions>().Configure<IServiceProvider>((option, service) => 
        {
        });
    }
}