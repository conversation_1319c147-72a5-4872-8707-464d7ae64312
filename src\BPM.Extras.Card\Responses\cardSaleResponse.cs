﻿namespace BPM.Extras.Card.Responses;

/// <summary>
///  储值卡销售响应
/// </summary>
public class cardSaleResponse
{

    /// <summary>
    /// 交易单号
    /// </summary>
    public string tradeNo { get; set; }

    /// <summary>
    /// 交易时间
    /// </summary>
    public string tradeTime { get; set; }


    /// <summary>
    /// 交易卡号列表
    /// </summary>
    public List<tradeCards> tradeCardList { get; set; }


    /// <summary>
    /// 卡号实体
    /// </summary>

    public class tradeCards
    {
        /// <summary>
        /// 序列号
        /// </summary>
        public int serialNumber { get; set; }

        /// <summary>
        /// 卡号
        /// </summary>
        public string cardNo { get; set; }


        /// <summary>
        /// 消费金额（单位：分）
        /// </summary>
        public int saleAmount { get; set; }


    }

}
