﻿using BPM.AspNetCore.Mvc;
using BPM.Caching;
using BPM.Core.Properties;
using BPM.Extensions;
using BPM.Extras.Card.Common;
using BPM.Extras.Card.Options;
using BPM.Extras.Card.Request;
using BPM.Extras.Card.Responses;
using BPM.Extras.Card.Result;
using BPM.Utils.Json;
using Domain.Shared;
using Microsoft.Extensions.Options;
using System.Net;
using System.Text;
using System.Text.Json;

namespace BPM.Extras.Card.Services;

/// <summary>
/// 有赞服务
/// </summary>
public class CardService : ICardService
{


    /// <summary>
    /// 缓存服务接口
    /// </summary>
    private readonly ICache _cache;

    /// <summary>
    ///储值卡配置.
    /// </summary>
    private readonly CardOptions _cardOptions;


    /// <summary>
    /// htpp工厂接口
    /// </summary>
    private readonly IHttpClientFactory _httpClientFactory;
    /// <summary>
    /// 访问令牌.
    /// </summary>
    public string cardToken { get; private set; }

    /// <summary>
    /// 初始化一个<see cref="CardService"/>类型的新实例.
    /// </summary>
    /// <param name="cache">缓存服务</param>
    /// <param name="cardOptions">有赞配置</param>
    public CardService(ICache cache, IOptions<CardOptions> cardOptions, IHttpClientFactory httpClientFactory)
    {
        _cache = cache ?? throw new ArgumentNullException(nameof(cache));
        _cardOptions = cardOptions.Value ?? throw new ArgumentNullException(nameof(cardOptions));
        _httpClientFactory = httpClientFactory;
    }

    /// <summary>
    /// 有赞token.
    /// </summary>
    /// <returns></returns>
    public async Task<ApiResult> GetTokenAsync()
    {
        var res = new ApiResult(StatusCode.Fail);
        //查询缓存是否存在token
        string token = await _cache.GetAsync<string>(CacheKey.cacheCardToken + _cardOptions.app_id);
        //缓存中存在token
        if (!token.IsEmpty())
        {
            res.Data = token;
            res.Code = (int)StatusCode.Ok;
            return res;
        }
        // 请求体数据
        var dict = new Dictionary<string, object>();
        dict.Add("client_id", _cardOptions.app_id);
        dict.Add("client_secret", _cardOptions.app_secret);
        dict.Add("authorize_type", _cardOptions.authorize_type);
        string signKey = _cardOptions.sign_key;
        string url = _cardOptions.service_url + "/youy/auth/access_token"; // API 端点
        string response = await Helpers.Web.Client().Post(url).JsonData(dict).ResultAsync();
        var result = response.ToObject<CardResult<TokenResponse>>();
        if (result.code == "200" || result.code == "1")
        {
            var token_info = result.data;
            long expires_in = long.Parse(token_info.expires);//缓存时间(分钟)
            expires_in = expires_in / 1000;
            //Token存储缓存
            await _cache.AddAsync<string>(CacheKey.cacheCardToken + _cardOptions.app_id, token_info.access_token, TimeSpan.FromHours(2));
            res.Code = (int)StatusCode.Ok;
            res.Data = token_info.access_token;
            return res;
        }
        else
            return new ApiResult((int)StatusCode.Fail, result.message);
    }

    /// <summary>
    /// 获取储值卡数据
    /// </summary>
    /// <param name="url">请求地址</param>
    /// <param name="dict">请求参数</param>
    /// <returns></returns>
    public async Task<string> getCradData_bak(string url, Dictionary<string, object> dict)
    {
        var res = await GetTokenAsync();
        if (res.Code == (int)StatusCode.Ok)
            cardToken = res.Data;
        string timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString();
        string sign = CardSignatureClient.GenerateSignature(JsonSerializer.Serialize(dict), _cardOptions.sign_key, timestamp);
        // 将签名和时间戳加入到URL查询参数中
        string urlWithParams = $"{url}?timestamp={timestamp}&sign={sign}";
        return await Helpers.Web.Client().Post(urlWithParams).OnFail(async (responseContent, statusCode) =>
         {
             // 401 未授权
             if (statusCode == HttpStatusCode.Unauthorized)
             {
                 res = await GetTokenAsync();
                 if (res.Code == (int)StatusCode.Ok)
                     cardToken = res.Data;
                 await Helpers.Web.Client().Post(urlWithParams).BearerToken(cardToken).JsonData(dict).ResultAsync();
             }
         }).BearerToken(cardToken).JsonData(dict).ResultAsync();

    }

    /// <summary>
    /// 获取储值卡数据
    /// </summary>
    /// <param name="url">请求地址</param>
    /// <param name="dict">请求参数</param>
    /// <returns></returns>
    public async Task<string> getCradData(string url, Dictionary<string, object> dict)
    {
        var res = await GetTokenAsync();
        if (res.Code == (int)StatusCode.Ok)
            cardToken = res.Data;
        this_goto:
        string timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString();
        string sign = CardSignatureClient.GenerateSignature(JsonSerializer.Serialize(dict), _cardOptions.sign_key, timestamp);
        // 将签名和时间戳加入到URL查询参数中
        string urlWithParams = $"{url}?timestamp={timestamp}&sign={sign}";
        var content = new StringContent(dict.ToJson(), Encoding.UTF8, "application/json");
        var client = _httpClientFactory.CreateClient();
        client.DefaultRequestHeaders.Add("Authorization", "Bearer " + cardToken);
        var response = await client.PostAsync(urlWithParams, content);
        if (response.StatusCode == HttpStatusCode.Unauthorized)
        {
            res = await GetTokenAsync();
            if (res.Code == (int)StatusCode.Ok)
                cardToken = res.Data;
            goto this_goto;
        }
        return await response.Content.ReadAsStringAsync();
    }

    /// <summary>
    /// 获取储值卡数据
    /// </summary>
    /// <param name="url">请求地址</param>
    /// <param name="dict">请求参数</param>
    /// <returns></returns>
    public async Task<CardResult<object>> getCradData(CardParameter parameter)
    {
        var res = await GetTokenAsync();
        if (res.Code == (int)StatusCode.Ok)
            cardToken = res.Data;
        else
        {
            return new CardResult<object>()
            {
                code = res.Code.ToString(),
                message = res.info
            };
        }
    this_goto:
        string timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString();
        string sign = CardSignatureClient.GenerateSignature(JsonSerializer.Serialize(parameter.body), _cardOptions.sign_key, timestamp);
        // 将签名和时间戳加入到URL查询参数中
        string urlWithParams = $"{parameter.url}?timestamp={timestamp}&sign={sign}";
        var content = new StringContent(parameter.body.ToJson(), Encoding.UTF8, "application/json");
        var client = _httpClientFactory.CreateClient();
        client.DefaultRequestHeaders.Add("Authorization", "Bearer " + cardToken);
        var response = await client.PostAsync(urlWithParams, content);
        if (response.StatusCode == HttpStatusCode.Unauthorized)
        {
            // 清除 Token缓存
            await _cache.RemoveAsync(CacheKey.cacheCardToken + _cardOptions.app_id);
            // 重新获取 token
            res = await GetTokenAsync();
            if (res.Code == (int)StatusCode.Ok)
                cardToken = res.Data;
            else
            {
                return new CardResult<object>()
                {
                    code = "500",
                    message = res.info
                };
            }
            goto this_goto;
        }
        var resultString = await response.Content.ReadAsStringAsync();
        return resultString.ToObject<CardResult<object>>();
    }
}