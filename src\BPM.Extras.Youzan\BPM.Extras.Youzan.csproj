﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="8.0.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Domain.Shared\Domain.Shared.csproj" />
    <ProjectReference Include="..\EventHandlers\EventHandlers.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="BPM.AspNetCore">
      <HintPath>..\..\dlls\BPM.AspNetCore.dll</HintPath>
    </Reference>
    <Reference Include="BPM.Caching.CSRedis">
      <HintPath>..\..\dlls\BPM.Caching.CSRedis.dll</HintPath>
    </Reference>
    <Reference Include="BPM.Core">
      <HintPath>..\..\dlls\BPM.Core.dll</HintPath>
    </Reference>
    <Reference Include="BPM.Logs">
      <HintPath>..\..\dlls\BPM.Logs.dll</HintPath>
    </Reference>
    <Reference Include="BPM.Utils">
      <HintPath>..\..\dlls\BPM.Utils.dll</HintPath>
    </Reference>
    <Reference Include="BPM.Utils.Http">
      <HintPath>..\..\dlls\BPM.Utils.Http.dll</HintPath>
    </Reference>
  </ItemGroup>

</Project>
