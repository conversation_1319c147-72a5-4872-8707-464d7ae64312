﻿using BPM.Extras.Youzan.Options;
using BPM.Extras.Youzan.Services;

namespace BPM.Extras.Youzan.Extensions;

/// <summary>
/// 有赞服务扩展类
/// </summary>
public static class YouzanServiceCollectionExtensions
{
    /// <summary>
    /// 添加有赞服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns></returns>
    public static IServiceCollection AddYouzan(this IServiceCollection services)
    {
        ConfigureYouzanOptions(services);
        services.AddTransient<IYouzanService, YouzanService>();
        return services;
    }

    /// <summary>
    /// 添加有赞配置
    /// </summary>
    /// <param name="services"></param>
    private static void ConfigureYouzanOptions(IServiceCollection services)
    {
        var configuration = services.GetConfiguration();
        var options = configuration.GetSection(nameof(YouzanOptions)).Value;
        // 配置验证
        services.AddOptions<YouzanOptions>().Configure<IServiceProvider>((option, service) =>
        {
        });
    }
}