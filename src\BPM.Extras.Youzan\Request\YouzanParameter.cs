﻿namespace BPM.Extras.Youzan.Request;

public class YouzanParameter
{
    /// <summary>
    /// 路径.
    /// </summary>
    public string url { get; set; }

    /// <summary>
    /// 请求方式.
    /// </summary>
    public string method { get; set; }

    /// <summary>
    /// 版本.
    /// </summary>
    public string version { get; set; } = "3.0.0";

    /// <summary>
    ///  门店id
    /// </summary>
    public string grant_id { get; set; } = "";

    /// <summary>
    /// 参数.
    /// </summary>
    public object body { get; set; }
    
    /// <summary>
    /// 访问令牌(可选)，如果提供则优先使用，否则使用系统默认Token
    /// </summary>
    public string access_token { get; set; }
}
