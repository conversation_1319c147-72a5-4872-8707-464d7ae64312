﻿namespace BPM.Extras.Youzan.Request;

/// <summary>
///  客户优惠券列表请求
/// </summary>
public class memberCouponListRequest
{
    /// <summary>
    ///  每页数量最大值：200
    /// </summary>
    public int page_size { get; set; } = 20;

    /// <summary>
    /// 起始页码：1
    /// </summary>
    public int page_num { get; set; } = 1;

    /// <summary>
    ///活动类型分组，1：优惠券，2：优惠码
    /// </summary>
    public int activity_type_group { get; set; } = 1;

    /// <summary>
    /// 手机号
    /// </summary>
    public string mobile { get; set; }

    /// <summary>
    /// yz_open_id
    /// </summary>
    public string yz_open_id { get; set; }

    /// <summary>
    /// 状态：0.全部1.生效=未使用and未过期2.已使用3.已过期=未使用and已过期4.已失效=已使用or已过期
    /// </summary>
    public int? status { get; set; } = 1;

    /// <summary>
    /// 券优惠属性 1:代金券 2:折扣券 3:兑换券 4:买赠券 5:运费抵扣券
    /// </summary>
    public List<int> preferential_mode_list { get; set; } = new List<int> { 1, 2, 3, 4, 5 };
}

/// <summary>
/// 订单优惠券凭证
/// </summary>
public class orderCouponRequest
{


    /// <summary>
    /// 买家身份标识
    /// </summary>
    public buyerInfo buyer_identity { get; set; } = new buyerInfo();

    /// <summary>
    /// 订单条目信息
    /// </summary>
    public List<orderItems> order_items { get; set; } = new List<orderItems>();

    /// <summary>
    /// 下单渠道类型 1 - 网店渠道 2 - 门店渠道
    /// </summary>
    public int channel_type { get; set; } = 2;

    /// <summary>
    /// 核销渠道(QQ小程序: qq_mini_program , 爱逛供货渠道: live_supplier , 爱逛自营渠道: self_support, 线下门店: offline_exclusive_channel, 线下Pos : offline_pos , 群团团:quntuantuan, 视频号小店: video_xiaodian )
    /// </summary>
    public string verify_channel { get; set; } = "offline_pos";


    public class orderItems
    {
        /// <summary>
        /// 总部商品id。 如果是没有总部商品，那么这个值与item_id 的值一致。
        /// </summary>
        public string root_item_id { get; set; }

        /// <summary>
        /// 商品规格Id，微商城店铺商品规格标识（同一商品Id下，规格id唯一）可以通过youzan.item.get(商品详情) 获取。如果该商品是无规格商品，那么sku_id取 youzan.item.get的spu.spu_id参数。如果该商品是多规格商品，那么sku_id 取youzan.item.get的skus.sku_id。
        /// </summary>
        public string sku_id { get; set; }

        /// <summary>
        /// 商品Id，有赞生成的店铺下商品唯一id，平台唯一。可以通过列表接口如youzan.items.onsale.get （查询出售中商品）和 youzan.items.inventory.get （查询仓库中商品）获取
        /// </summary>
        public string item_id { get; set; }

        /// <summary>
        /// 商品原价，单位为分
        /// </summary>
        public long item_original_price { get; set; }

        /// <summary>
        /// 条目均摊订单级优惠后的价格
        /// </summary>
        public long order_item_promotional_price { get; set; }

        /// <summary>
        /// 条目下商品购买数量，可以通过youzan.item.get(商品详情) 获取
        /// </summary>
        public int num { get; set; }

        /// <summary>
        /// 商品类型
        /// </summary>
        public int item_type { get; set; } = 1;

        /// <summary>
        ///是参与了同一个营销活动的sku的唯一标识，没有接入促销前，此参数可以与sku_id保持一致。如果该商品是无规格商品，那么sku_id取 youzan.item.get的spu.spu_id参数。如果该商品是多规格商品，那么sku_id 取youzan.item.get的skus.sku_id 。
        /// </summary>
        public long order_item_id { get; set; }

    }

    /// <summary>
    /// 买家身份标识
    /// </summary>
    public class buyerInfo
    {
        /// <summary>
        /// openId
        /// </summary>
        public string yz_open_id { get; set; }
    }
}

/// <summary>
/// 订单优惠券核销
/// </summary>
public class orderCouponVerifyRequest
{
    /// <summary>
    /// 核销的订单号
    /// </summary>
    public string verify_in_order { get; set; }

    /// <summary>
    /// 优惠券码
    /// </summary>
    public string verify_code { get; set; }

    /// <summary>
    /// 券在订单上的优惠金额，单位:分
    /// </summary>
    public long preferential_value { get; set; }

    /// <summary>
    /// 总部门店id
    /// </summary>
    public string kdt_id { get; set; }

    /// <summary>
    /// 核销门店
    /// </summary>
    public string verified_kdt_id { get; set; }


}

/// <summary>
/// 订单优惠券退还
/// </summary>
public class orderCouponRevertRequest
{
    /// <summary>
    /// 优惠类型：0-优惠券；1-优惠码
    /// </summary>
    public int coupon_type { get; set; } = 1;

    /// <summary>
    /// 优惠券、优惠码唯一标识id，可以通过【youzan.ump.voucher.query】响应参数coupon_id获取
    /// </summary>
    public string coupon_id { get; set; }
}

/// <summary>
/// 叠加优惠凭证计算优惠
/// </summary>
public class orderCouponVerifyCalculateRequest
{
    /// <summary>
    /// 核销渠道，用来识别返回线上券线下券
    /// </summary>
    public string verify_channel { get; set; } = "offline_pos";

    /// <summary>
    /// 下单渠道类型 1 - 网店渠道 2 - 门店渠道
    /// </summary>
    public int channel_type { get; set; } = 2;

    /// <summary>
    /// openId
    /// </summary>
    public string yz_open_id { get; set; }

    /// <summary>
    /// 适用门店id
    /// </summary>
    public string kdt_id { get; set; }

    /// <summary>
    /// 凭证身份
    /// </summary>
  //  public voucherIdentity voucher_identity { get; set; }

    /// <summary>
    /// 订单条目信息
    /// </summary>
    public List<orderItems> order_items { get; set; } = new List<orderItems>();
    /// <summary>
    /// 凭证身份实体
    /// </summary>
    //public class voucherIdentity
    //{
    //    /// <summary>
    //    /// 优惠类型：0-优惠券；1-优惠码
    //    /// </summary>
    //    public int coupon_type { get; set; } = 1;


    //    /// <summary>
    //    /// 优惠券、优惠码唯一标识id，可以通过【youzan.ump.voucher.query】响应参数coupon_id获取
    //    /// </summary>
    //    public string coupon_id { get; set; }
    //}

    /// <summary>
    /// 叠加优惠券商品优惠信息-优惠券叠加必传
    /// </summary>
    public List<voucherPreferentialItemList> voucher_preferential_item_list { get; set; }

    /// <summary>
    /// 订单条目信息
    /// </summary>
    public class orderItems
    {
        /// <summary>
        /// 总部商品id。 如果是没有总部商品，那么这个值与item_id 的值一致。
        /// </summary>
        public string root_item_id { get; set; }

        /// <summary>
        /// 商品原价，单位为分
        /// </summary>
        public long goods_original_price { get; set; }

        /// <summary>
        /// 商品Id，有赞生成的店铺下商品唯一id，平台唯一。可以通过列表接口如youzan.items.onsale.get （查询出售中商品）和 youzan.items.inventory.get （查询仓库中商品）获取
        /// </summary>
        public string item_id { get; set; }

        /// <summary>
        /// 条目均摊订单级优惠后的价格
        /// </summary>
        public long goods_promotional_price { get; set; }

        /// <summary>
        /// 商品规格Id，微商城店铺商品规格标识（同一商品Id下，规格id唯一）可以通过youzan.item.get(商品详情) 获取。如果该商品是无规格商品，那么sku_id取 youzan.item.get的spu.spu_id参数。如果该商品是多规格商品，那么sku_id 取youzan.item.get的skus.sku_id。
        /// </summary>
        public string sku_id { get; set; }

        /// <summary>
        /// 订单条目id
        /// </summary>
        public long oid { get; set; }

        /// <summary>
        /// 该商品是否使用商品兑换券* true：使用* false：不使用* 不传：不使用 注意，商品兑换券只能用到一个商品上，不能同时多个商品，否则会报错
        /// </summary>
        public bool use_exchange_coupon { get; set; } = false;

        /// <summary>
        /// 条目下商品购买数量，可以通过youzan.item.get(商品详情) 获取
        /// </summary>
        public int num { get; set; }



    }

    /// <summary>
    /// 叠加优惠券商品优惠信息
    /// </summary>
    public class voucherPreferentialItemList
    {

        /// <summary>
        ///  优惠券优惠金额 单位：分
        /// </summary>
        public long preferential_value { get; set; }

        /// <summary>
        /// 优惠券叠加明细
        /// </summary>
        public List<voucherOrderItems> voucher_order_items { get; set; }

        /// <summary>
        /// 凭证身份
        /// </summary>
        public voucherIdentity voucher_identity { get; set; }

        /// <summary>
        /// 凭证身份实体
        /// </summary>
        public class voucherIdentity
        {
            /// <summary>
            /// 优惠类型：0-优惠券；1-优惠码
            /// </summary>
            public int coupon_type { get; set; } = 1;


            /// <summary>
            /// 优惠券、优惠码唯一标识id，可以通过【youzan.ump.voucher.query】响应参数coupon_id获取
            /// </summary>
            public string coupon_id { get; set; }
        }


        /// <summary>
        ///  优惠券叠加明细
        /// </summary>
        public class voucherOrderItems
        {
            /// <summary>
            ///  商品价格，商品级促销优惠后的价格
            /// </summary>
            public long goods_preferential_value { get; set; }

            /// <summary>
            /// 商品Id，有赞生成的店铺下商品唯一id，
            /// </summary>
            public string item_id { get; set; }

            /// <summary>
            /// 商品规格Id
            /// </summary>
            public string sku_id { get; set; }

            /// <summary>
            /// 订单条目id
            /// </summary>
            public long oid { get; set; }

            /// <summary>
            /// 总部商品id。 如果是没有总部商品，那么这个值与item_id 的值一致。
            /// </summary>
            public string root_item_id { get; set; }

        }
    }
}