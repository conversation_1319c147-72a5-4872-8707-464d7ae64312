﻿namespace BPM.Extras.Youzan.Request;

/// <summary>
///  客户详情请求
/// </summary>
public class customerDetailRequest
{
    /// <summary>
    /// 需要获得的用户属性，使用","隔开，包含以下user_base（基础信息）,tags（标签）,benefit_cards（权益卡）,benefit_level（等级）,benefit_rights（权益）,credit（积分）,behavior（交易行为）,giftcard（礼品卡）,prepaid（储值）,coupon（优惠券）,level（会员等级信息）,auth_info（授权信息）
    /// </summary>
    public string fields { get; set; }

    /// <summary>
    ///有赞用户id，用户在有赞的唯一id。推荐使用
    /// </summary>
    public string yz_open_id { get; set; }

    /// <summary>
    /// 用户帐号信息（yz_open_id和帐号ID不能同时为空）
    /// </summary>
    public accountInfo account_info { get; set; } = new accountInfo();

    /// <summary>
    /// 扩展点
    /// </summary>
    public bool is_do_ext_point { get; set; } = false;
}

/// <summary>
/// 客户权益卡请求
/// </summary>
public class customerEquityRequest
{
    /// <summary>
    /// 是否查询所有（false-查可用权益卡;true-查全部权益卡）默认查可用权益卡
    /// </summary>
    public bool state { get; set; } = true;

    /// <summary>
    /// 三方应用client_id
    /// </summary>
    public string client_id { get; set; }

    /// <summary>
    /// 用户信息
    /// </summary>
    public accountInfo user { get; set; } = new accountInfo();

    /// <summary>
    /// 页数
    /// </summary>
    public int page_no { get; set; } = 1;
}

public class accountInfo
{
    /// <summary>
    /// 帐号ID
    /// </summary>
    public string account_id { get; set; }

    /// <summary>
    /// 帐号类型（支持的用户账号类型 1-有赞粉丝id(有赞不同的合作渠道会生成不同渠道对应在有赞平台下的fans_id); 2-手机号; 4-union_id(同一用户，对同一个微信开放平台下的不同应用，unionid是相同的); 5-yz_open_id，推荐使用））
    /// </summary>
    public int account_type { get; set; } = 2;

}
