﻿namespace BPM.Extras.Youzan.Request;

/// <summary>
///  新增用户积分
/// </summary>
public class customerPointRequest
{
    /// <summary>
    ///  积分变动原因
    /// </summary>
    public string reason { get; set; }

    /// <summary>
    /// 积分变更店铺id
    /// </summary>
    public long source_kdt_id { get; set; }

    /// <summary>
    ///  积分变动值
    /// </summary>
    public int points { get; set; }

    /// <summary>
    ///  用户信息
    /// </summary>
    public userInfo user { get; set; }

    /// <summary>
    ///  业务唯一标示，保证重复请求只增加一次积分（订单号）
    /// </summary>
    public string biz_value { get; set; }

    public class userInfo
    {
        /// <summary>
        /// 帐号类型（支持的用户账号类型 1-有赞粉丝id(有赞不同的合作渠道会生成不同渠道对应在有赞平台下的fans_id); 2-手机号; 3-三方帐号(原open_user_id:三方App用户ID，该参数仅限购买App开店插件的商家使用);5-有赞用户id，用户在有赞的唯一id。推荐使用）
        /// </summary>
        public int account_type { get; set; } = 2;

        /// <summary>
        ///帐号ID
        /// </summary>
        public string account_id { get; set; }
    }
}
