﻿namespace BPM.Extras.Youzan.Request;

/// <summary>
///  订单退款
/// </summary>
public class orderRefundRequest
{
    /// <summary>
    /// 三方单据退款诉求(退货方式)：1-仅退款，2-退货退款，3-换货；
    /// </summary>
    public int out_order_demand { get; set; } = 2;

    /// <summary>
    /// 三方单据单号
    /// </summary>
    public string out_order_no { get; set; }

    /// <summary>
    /// 三方单据类型。1-无源单退款单
    /// </summary>
    public int out_order_type { get; set; } = 1;

    /// <summary>
    ///三方单据费用，单位分。
    /// </summary>
    public long out_order_price { get; set; }

    /// <summary>
    /// 三方单据物流方式：0-快递，1-自提，2-同城配送，9.-需物流快递；
    /// </summary>
    public int out_order_logistics_type { get; set; } = 1;

    /// <summary>
    /// 单据所产生的店铺对应的kdtId，和shop_code至少有一个入参，同时入参时，接口会报错。
    /// </summary>
    public long origin_kdt_id { get; set; }

    /// <summary>
    /// 三方单据发生时间。如果不传，默认使用当前时间。格式yyyy-MM-dd HH:mm:ss
    /// </summary>
    public DateTime out_order_time { get; set; }

    /// <summary>
    ///  用户信息
    /// </summary>
    public userInfo user { get; set; }

    /// <summary>
    /// 退款明细
    /// </summary>
    public List<refundItems> items { get; set; }

    public class userInfo
    {
        /// <summary>
        /// 帐号类型（支持的用户账号类型 1-有赞粉丝id(有赞不同的合作渠道会生成不同渠道对应在有赞平台下的fans_id); 2-手机号; 3-三方帐号(原open_user_id:三方App用户ID，该参数仅限购买App开店插件的商家使用);5-有赞用户id，用户在有赞的唯一id。推荐使用）
        /// </summary>
        public int account_type { get; set; } = 2;

        /// <summary>
        ///帐号ID
        /// </summary>
        public string account_id { get; set; }
    }


    /// <summary>
    /// 退款明细
    /// </summary>
    public class refundItems
    {
        /// <summary>
        /// 商品实付金额，单位:分
        /// </summary>
        public int current_unit_price { get; set; }

        /// <summary>
        /// 外部商品在订单中生成的唯一id，即订单条目id；
        /// </summary>
        public string out_order_item_id { get; set; }

        /// <summary>
        /// 商品实付总金额，单位:分
        /// </summary>
        public int current_total_price { get; set; }

       

        /// <summary>
        /// 商品原价总金额，单位:分
        /// </summary>
        public int origin_total_price { get; set; }

        /// <summary>
        /// 商品原始价格（单位：分）
        /// </summary>
        public int origin_unit_price { get; set; }

        /// <summary>
        ///  数量
        /// </summary>
        public int num { get; set; }

        /// <summary>
        ///  有赞内部商品id
        /// </summary>
        public string item_id { get; set; }

        /// <summary>
        /// 有赞内部skuId
        /// </summary>
        public string sku_id { get; set; }

        /// <summary>
        /// 商品名称
        /// </summary>
        public string title { get; set; }

        /// <summary>
        /// 商品条码，仅做展示使用
        /// </summary>
        public string barcode { get; set; }
    }
}
