﻿namespace BPM.Extras.Youzan.Request;

/// <summary>
///  订单交易请求
/// </summary>
public class orderTradeRequest
{

    /// <summary>
    /// 订单主体
    /// </summary>
    public oderBaseInfo main_info { get; set; }

    /// <summary>
    /// 订单价格
    /// </summary>
    public orderPrice order_price { get; set; }
    /// <summary>
    /// 买家信息
    /// </summary>
    public buyerInfo buyer { get; set; }

    /// <summary>
    /// 卖家信息
    /// </summary>
    public sellerInfo seller { get; set; }

    /// <summary>
    /// 交易明细
    /// </summary>
    public List<tradeItems> trade_items { get; set; }

    /// <summary>
    ///  优惠信息
    /// </summary>
    public List<promotionInfo> promotions { get; set; }

    /// <summary>
    /// 收货人信息
    /// </summary>
    public receiverInfo receiver { get; set; }

    /// <summary>
    /// 订单主体
    /// </summary>
    public class oderBaseInfo
    {
        /// <summary>
        /// 订单状态 创建:CREATED 待发货已确认:CONFIRMED， 交易成功:SUCCESS(仅线下订单支持直接将订单推进到已完成，如POS机订单）
        /// </summary>
        public string order_status { get; set; } = "SUCCESS";

        /// <summary>
        /// 订单创建时间（时间格式 yyyy-MM-dd HH:mm:ss）
        /// </summary>
        public DateTime? create_time { get; set; }

        /// <summary>
        /// 订单创建时间（时间格式 yyyy-MM-dd HH:mm:ss）
        /// </summary>
        public DateTime? pay_time { get; set; }

        /// <summary>
        /// 外部订单号；必须是字符或数字，长度不能超过60
        /// </summary>
        public string out_biz_no { get; set; }

        /// <summary>
        /// 订单来源
        /// </summary>
        public OpenSource open_source { get; set; }
    }

    public class OpenSource
    {
        /// <summary>
        /// 订单状态
        /// java.lang.String
        /// 必填: 是
        /// 订单状态 创建:CREATED 待发货已确认:CONFIRMED， 交易成功:SUCCESS(仅线下订单支持直接将订单推进到已完成，如POS机订单）
        /// </summary>
        public string order_status { get; set; } = "SUCCESS";

        /// <summary>
        /// 交易终端
        /// java.lang.String
        /// 必填: 否
        /// 交易终端，具体产生交易行为的地方，枚举型，传值不正确时会报错【交易终端不存在】
        /// 具体枚举值如下:H5、微信小程序、支付宝小程序、PC、App、POS
        /// </summary>
        public string trade_mark { get; set; }

        /// <summary>
        /// 交易渠道
        /// </summary>
        public TradeChannel trade_channel { get; set; }
    }

    public class TradeChannel
    {
        /// <summary>
        /// 通用渠道
        /// java.lang.String
        /// 必填: 否
        /// 通用渠道，枚举型，传值不正确时会报错【通用渠道不存在】
        /// 具体枚举值如下(请传入英文)：
        /// 淘宝（Taobao）、天猫（TMALL）、京东（JD）、拼多多（pinduoduo）、
        /// 苏宁易购（suning）、微盟（Weimob）、抖音（douyin）、快手（kuaishou）、
        /// 线下门店（POS）、三方ERP（ERP）、美团（meituan）、小红书（xiaohongshu）
        /// </summary>
        public string common_channel { get; set; }
    }

    /// <summary>
    /// 订单价格信息
    /// </summary>
    public class orderPrice
    {
        /// <summary>
        /// 实付总价（单位：分）totalPrice = currentPrice + postage
        /// </summary>
        public int total_price { get; set; }

        /// <summary>
        /// 优惠金额（单位：分）
        /// </summary>
        public int promotion_amount { get; set; }

        /// <summary>
        ///  订单原价（单位：分）
        /// </summary>
        public int origin_price { get; set; }

        /// <summary>
        /// 订单现价（单位：分）
        /// </summary>
        public int current_price { get; set; }
    }

    /// <summary>
    /// 卖家信息
    /// </summary>
    public class sellerInfo
    {
        /// <summary>
        /// 店铺名称
        /// </summary>
        public string shop_name { get; set; }

        /// <summary>
        /// 有赞店铺id
        /// </summary>
        public long kdt_id { get; set; }

        /// <summary>
        /// 卖家店铺id
        /// </summary>
        public string out_kdt_id { get; set; }

    }

    /// <summary>
    /// 买家信息
    /// </summary>
    public class buyerInfo
    {
        /// <summary>
        /// 买家姓名
        /// </summary>
        public string buyer_name { get; set; }

        /// <summary>
        /// 买家手机号
        /// </summary>
        public string tel { get; set; }

        /// <summary>
        /// yz_open_id
        /// </summary>
        public string yz_open_id { get; set; }
    }

    /// <summary>
    /// 交易明细
    /// </summary>
    public class tradeItems
    {
        /// <summary>
        ///  有赞内部商品id
        /// </summary>
        [System.Text.Json.Serialization.JsonIgnore]
        [Newtonsoft.Json.JsonIgnore]
        public string item_id { get; set; }

        /// <summary>
        /// 商品订单实付金额（单位：分）
        /// </summary>
        public int current_total_amount { get; set; }

        /// <summary>
        /// 商品当前价格（单位：分）
        /// </summary>
        public int current_price { get; set; }

        /// <summary>
        /// 商品原始价格（单位：分）
        /// </summary>
        public int origin_price { get; set; }

        /// <summary>
        ///  数量
        /// </summary>
        public int num { get; set; }

        /// <summary>
        /// 有赞内部skuId
        /// </summary>
        [System.Text.Json.Serialization.JsonIgnore]
        [Newtonsoft.Json.JsonIgnore]
        public string sku_id { get; set; }

        /// <summary>
        /// 商品信息
        /// </summary>
        public goodsInfo goods_info { get; set; }

        public class goodsInfo
        {
            /// <summary>
            /// 商品名称
            /// </summary>
            public string title { get; set; }

            /// <summary>
            ///  商品编码
            /// </summary>
            public string item_no { get; set; }

            /// <summary>
            /// 规格id
            /// </summary>
            [Newtonsoft.Json.JsonProperty(NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
            [System.Text.Json.Serialization.JsonIgnore(Condition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull)]
            public string sku_no { get; set; }
        }
    }

    /// <summary>
    ///  优惠信息
    /// </summary>
    public class promotionInfo
    {
        /// <summary>
        ///  优惠总额
        /// </summary>
        public int? amount { get; set; }

        /// <summary>
        /// 优惠名称
        /// </summary>
        public string name { get; set; }
    }

    /// <summary>
    /// 收货人信息
    /// </summary>
    public class receiverInfo
    {

        /// <summary>
        /// 收件人
        /// </summary>
        public string receiver_name { get; set; } = "门店自提";

        /// <summary>
        /// 物流类型
        /// </summary>
        public string logistics_type { get; set; } = "NONE";
    }
}
