﻿using static BPM.Extras.Youzan.Request.orderCouponVerifyCalculateRequest.voucherPreferentialItemList;

namespace BPM.Extras.Youzan.Responses;

/// <summary>
///  客户优惠券
/// </summary>
public class customerCouponResponse
{

    /// <summary>
    /// 代码
    /// </summary>
    public string verify_code { get; set; }

    /// <summary>
    /// 标题
    /// </summary>
    public string title { get; set; }

    /// <summary>
    /// 描写
    /// </summary>
    public string description { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public int status { get; set; }

}

/// <summary>
/// 订单优惠券响应
/// </summary>

public class orderCouponResponse
{
    /// <summary>
    /// 凭证身份
    /// </summary>
    public Voucher_identity voucher_identity { get; set; }
    /// <summary>
    /// 有效开始时间，时间格式：yyyy-MM-dd-HH-mm-ss
    /// </summary>
    public long valid_start_time { get; set; }
    /// <summary>
    ///  订单优惠价格优惠凭证在订单上的优惠额
    /// </summary>
    public long order_preferential_value { get; set; }
    /// <summary>
    /// 是否可用
    /// </summary>
    public bool available { get; set; }
    /// <summary>
    /// 门槛值
    /// </summary>
    public long threshold { get; set; }
    /// <summary>
    /// 核销码
    /// </summary>
    public string verify_code { get; set; }
    /// <summary>
    /// 凭证面额
    /// </summary>
    public long voucher_value { get; set; }
    /// <summary>
    /// 门槛文案
    /// </summary>
    public string threshold_copywriter { get; set; }
    /// <summary>
    ///标题
    /// </summary>
    public string title { get; set; }
    /// <summary>
    ///  优惠券适用的商品列表
    /// </summary>
    public List<long> optimal_preferential_order_item_ids { get; set; }
    /// <summary>
    /// 凭证优惠方式
    /// </summary>
    public int preferential_mode { get; set; }
    /// <summary>
    /// 优惠券活动id
    /// </summary>
    public string activity_id { get; set; }
    /// <summary>
    /// 码值
    /// </summary>
    public string code_value { get; set; }
    /// <summary>
    /// 折扣优惠方式最大折扣上限
    /// </summary>
    public long max_discount { get; set; }
    /// <summary>
    /// 有效结束时间，时间格式：yyyy-MM-dd-HH-mm-ss
    /// </summary>
    public long valid_end_time { get; set; }
    /// <summary>
    /// 面值描述
    /// </summary>
    public string value_desc { get; set; }
    /// <summary>
    /// 单位描述
    /// </summary>
    public string unit_desc { get; set; }
    /// <summary>
    /// 是否与其它券叠加
    /// </summary>
    public bool can_overlay_with_other { get; set; }
    /// <summary>
    /// 是否与自身叠加
    /// </summary>
    public bool can_overlay_with_self { get; set; }

    /// <summary>
    /// 不可用原因
    /// </summary>
    public string reason { get; set; }

    /// <summary>
    /// 凭证身份
    /// </summary>
    public class Voucher_identity
    {
        /// <summary>
        /// 凭证id
        /// </summary>
        public string coupon_id { get; set; }
        /// <summary>
        /// 凭证类型，0：优惠券，1：优惠码
        /// </summary>
        public int coupon_type { get; set; }
    }
}
/// <summary>
/// 订单验证优惠券响应
/// </summary>
public class orderCouponVerifyResponse
{
    /// <summary>
    /// 凭证身份标识
    /// </summary>
    public Voucher_identity voucher_identity { get; set; }
    /// <summary>
    /// 发放来源
    /// </summary>
    public string send_source { get; set; }

    /// <summary>
    /// 有效开始时间;单位:秒
    /// </summary>
    public long valid_start_time { get; set; }

    /// <summary>
    /// 发放店铺id
    /// </summary>
    public long send_kdt_id { get; set; }

    /// <summary>
    /// 有赞用户id
    /// </summary>
    public string yz_open_id { get; set; }

    /// <summary>
    /// 核销码
    /// </summary>
    public string verify_code { get; set; }

    /// <summary>
    /// 发放时间;单位:秒
    /// </summary>
    public long sent_at { get; set; }

    /// <summary>
    /// 店铺在有赞的id标识
    /// </summary>
    public long kdt_id { get; set; }

    /// <summary>
    /// 优惠方式，1：代金券，2：折扣券，3：兑换券
    /// </summary>
    public int preferential_mode { get; set; }

    /// <summary>
    /// 内部优惠券活动id
    /// </summary>
    public long activity_id { get; set; }

    /// <summary>
    /// 有效结束时间;单位:秒
    /// </summary>
    public long valid_end_time { get; set; }

    /// <summary>
    /// 面额;单位:分
    /// </summary>
    public long value { get; set; }

    /// <summary>
    /// 凭证状态，1：正常2：已冻结，3：已核销，4：不可用，5：已删除
    /// </summary>
    public int status { get; set; }

    /// <summary>
    /// 凭证身份标识
    /// </summary>
    public class Voucher_identity
    {
        /// <summary>
        /// 老凭证id
        /// </summary>
        public long coupon_id { get; set; }

        /// <summary>
        /// 老凭证类型，0：优惠券，1：优惠码
        /// </summary>
        public long coupon_type { get; set; }
    }
}

/// <summary>
/// 订单撤销响应
/// </summary>
public class orderCouponRevertResponse
{

}

/// <summary>
///  优惠券验核响应实体
/// </summary>
public class orderCouponCheckVerifyResponse
{

    public VoucherIdentity voucher_identity { get; set; }

    /// <summary>
    /// 不可用原因
    /// </summary>
    public string reason { get; set; }

    /// <summary>
    ///  有效开始时间
    /// </summary>
    public long valid_start_time { get; set; }

    /// <summary>
    /// 有效结束时间，时间格式：yyyy-MM-dd-HH-mm-ss
    /// </summary>
    public long valid_end_time { get; set; }

    /// <summary>
    /// 订单优惠价格优惠凭证在订单上的优惠额
    /// </summary>
    public int order_preferential_value { get; set; }

    /// <summary>
    /// 是否可用
    /// </summary>
    public bool available { get; set; }

    /// <summary>
    /// 门槛值
    /// </summary>
    public int threshold { get; set; }

    /// <summary>
    /// 凭证面额
    /// </summary>
    public int voucher_value { get; set; }

    /// <summary>
    /// 门槛文案
    /// </summary>
    public string threshold_copywriter { get; set; }

    /// <summary>
    /// 标题
    /// </summary>
    public string title { get; set; }

    /// <summary>
    /// 折扣优惠方式最大折扣上限
    /// </summary>
    public int maxDiscount { get; set; }
    /// <summary>
    ///凭证优惠方式
    /// </summary>
    public int preferential_mode { get; set; }
    /// <summary>
    /// 凭证所属活动id
    /// </summary>
    public long activity_id { get; set; }
    /// <summary>
    /// 面值描述
    /// </summary>
    public string value_desc { get; set; }

    /// <summary>
    /// 商品均摊金额
    /// </summary>
    public List<GoodsPreferential> goods_preferential_list { get; set; }

    /// <summary>
    /// 单位描述
    /// </summary>
    public string unit_desc { get; set; }

    /// <summary>
    ///  验证身份
    /// </summary>
    public class VoucherIdentity
    {
        public string coupon_id { get; set; }
        public int coupon_type { get; set; }
    }

    /// <summary>
    /// 商品均摊金额
    /// </summary>
    public class GoodsPreferential
    {
        public int goods_preferential_value { get; set; }
        public long item_id { get; set; }
        public long sku_id { get; set; }
        public long oid { get; set; }
    }

}