﻿namespace BPM.Extras.Youzan.Responses;

/// <summary>
///  客户详情响应
/// </summary>
public class customerDetailResponse
{
    /// <summary>
    ///  门店号
    /// </summary>
    public string ascription_kdt_id { get; set; }
    /// <summary>
    /// 客户权益卡
    /// </summary>
    public List<equityCardInfo> cards { get; set; }
    /// <summary>
    /// 性别
    /// </summary>
    public int gender { get; set; }

    /// <summary>
    /// 展示姓名(包含展示逻辑：客户姓名=>手机号=>微信昵称=>匿名用户)
    /// </summary>
    public string show_name { get; set; }
    /// <summary>
    /// 昵称和B端店铺后台客户详情昵称一致，推荐使用
    /// </summary>
    public string latest_nickname { get; set; }
    /// <summary>
    ///  创建日期
    /// </summary>
    public long created_at { get; set; }
    /// <summary>
    /// open_id
    /// </summary>
    public string yz_open_id { get; set; }
    /// <summary>
    /// 积分
    /// </summary>
    public int points { get; set; } = 0;
    /// <summary>
    /// 更新时间
    /// </summary>
    public long updated_at { get; set; }

    /// <summary>
    /// 成为会员来源渠道
    /// </summary>
    public int member_source_channel { get; set; }
    /// <summary>
    /// 微信昵称
    /// </summary>
    public string wx_nickname { get; set; }
    /// <summary>
    /// 累计消费积分数量
    /// </summary>
    public int total_consumer_points { get; set; }
    /// <summary>
    /// 自定义信息项
    /// </summary>
    public List<customerAttrInfos> customer_attrInfos { get; set; }
    /// <summary>
    /// 成为会员当时的门店Id
    /// </summary>
    public int member_source_kdt_id { get; set; }
    /// <summary>
    /// 成为客户来源渠道 0:"其他";1:"未知来源";10
    /// </summary>
    public int source_channel { get; set; }
    /// <summary>
    ///  手机号
    /// </summary>
    public string mobile { get; set; }
    /// <summary>
    /// 客户手机号国家区域码
    /// </summary>
    public string mobile_country_code { get; set; }

    /// <summary>
    /// 客户姓名
    /// </summary>
    public string name { get; set; }

    /// <summary>
    /// 生日
    /// </summary>
    public string birthday { get; set; }

    /// <summary>
    /// 认证信息
    /// </summary>
    public AuthInfo auth_info { get; set; }
}

/// <summary>
/// 认证信息
/// </summary>
public class AuthInfo
{
    /// <summary>
    /// 是否已认证手机号
    /// </summary>
    public bool is_mobile_auth { get; set; }
}

/// <summary>
/// 权益卡信息
/// </summary>
public class equityCardInfo
{
    /// <summary>
    /// 权益卡级别
    /// </summary>
    public int level { get; set; }
    /// <summary>
    /// 权益卡名称
    /// </summary>
    public string name { get; set; }
    /// <summary>
    /// 主键
    /// </summary>
    public int id { get; set; }
}
/// <summary>
/// 客户扩展信息
/// </summary>
public class customerAttrInfos
{
    /// <summary>
    /// 名称
    /// </summary>
    public string name { get; set; }

    /// <summary>
    /// 值
    /// </summary>
    public string value { get; set; }
}