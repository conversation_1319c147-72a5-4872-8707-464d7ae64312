﻿namespace BPM.Extras.Youzan.Responses;
/// <summary>
///  客户权益响应
/// </summary>
public class customerEquityResponse
{

    /// <summary>
    /// 
    /// </summary>
    public int total { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public int page { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public int page_size { get; set; }

    /// <summary>
    /// 权益卡明细
    /// </summary>
    public List<equityItems> items {  get; set; }   
}

/// <summary>
/// 权益卡明细
/// </summary>
public class equityItems
{
    /// <summary>
    ///  截至日期
    /// </summary>
    public string card_end_time { get; set; }
    /// <summary>
    /// 开始日期
    /// </summary>
    public string card_start_time { get; set; }
    /// <summary>
    /// 权益卡状态(0-未激活;1-使用中;2-已退款;3-已过期;4-用户已删除;5-商家已禁用;6-管理员删除;7-系统删除;8-未生效)
    /// </summary>
    public int card_state { get; set; }
    /// <summary>
    ///  权益卡号
    /// </summary>
    public string card_no { get; set; }
    /// <summary>
    /// 权益卡别名
    /// </summary>
    public string card_alias { get; set; }
}