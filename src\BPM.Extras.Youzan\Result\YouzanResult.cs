﻿namespace BPM.Extras.Youzan.Result;

/// <summary>
/// 有赞返回对象
/// </summary>
public class YouzanResult<T>
{
    public int total { get; set; } = 0;

    /// <summary>
    /// 跟踪id
    /// </summary>
    public string trace_id { get; set; }

    /// <summary>
    /// 交易代码
    /// </summary>
    public string code { get; set; }

    /// <summary>
    /// 是否成功
    /// </summary>
    public bool success { get; set; }

    /// <summary>
    /// 返回对象
    /// </summary>
    public T data { get; set; }

    /// <summary>
    /// 消息
    /// </summary>

    public string message { get; set; }


    /// <summary>
    /// 网关错误
    /// </summary>
    public gwErrResp gw_err_resp { get; set; }

    public class gwErrResp
    {
        /// <summary>
        /// 跟踪id
        /// </summary>
        public string trace_id { get; set; }

        /// <summary>
        /// 错误代码
        /// </summary>
        public string err_code { get; set; }

        /// <summary>
        /// 错误描述
        /// </summary>
        public string err_msg { get; set; }
        



    }

}

