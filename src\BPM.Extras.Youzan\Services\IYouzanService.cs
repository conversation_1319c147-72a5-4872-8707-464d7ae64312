﻿using BPM.AspNetCore.Mvc;
using BPM.Extras.Youzan.Request;
using BPM.Extras.Youzan.Result;

namespace BPM.Extras.Youzan.Services;

/// <summary>
/// 有赞服务
/// </summary>
public interface IYouzanService
{
    /// <summary>
    /// 获取储值卡数据
    /// </summary>
    /// <param name="url">请求地址</param>
    /// <param name="dict">请求参数</param>
    /// <returns></returns>
    Task<YouzanResult<object>> getYouzanData(YouzanParameter param);
}