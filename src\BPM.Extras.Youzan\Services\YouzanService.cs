﻿using BPM.AspNetCore.Mvc;
using BPM.Caching;
using BPM.Extensions;
using BPM.Extras.Youzan.Options;
using BPM.Extras.Youzan.Request;
using BPM.Extras.Youzan.Responses;
using BPM.Extras.Youzan.Result;
using BPM.Logs;
using BPM.Logs.Contents;
using BPM.Text;
using BPM.Utils.Json;
using Domain.Shared;
using Microsoft.Extensions.Options;
using System.Text;

namespace BPM.Extras.Youzan.Services;

/// <summary>
/// 有赞服务
/// </summary>
public class YouzanService : IYouzanService
{
    /// <summary>
    /// 日志服务
    /// </summary>
    private readonly ILog _log;

    /// <summary>
    /// 缓存服务接口
    /// </summary>
    private readonly ICache _cache;

    /// <summary>
    ///有赞配置.
    /// </summary>
    private readonly YouzanOptions _options;

    /// <summary>
    /// 访问令牌.
    /// </summary>
    private string youzanToken { get; set; }

    /// <summary>
    /// 初始化一个<see cref="YouzanService"/>类型的新实例.
    /// </summary>
    /// <param name="cache">缓存服务</param>
    /// <param name="cardOptions">有赞配置</param>
    /// <param name="log">日志服务</param>
    public YouzanService(ICache cache, IOptions<YouzanOptions> cardOptions, ILog log)
    {
        _cache = cache ?? throw new ArgumentNullException(nameof(cache));
        _options = cardOptions.Value ?? throw new ArgumentNullException(nameof(cardOptions));
        _log = log ?? throw new ArgumentNullException(nameof(log));
    }

    /// <summary>
    /// 获取有赞token.
    /// </summary>
    /// <returns></returns>
    private async Task<ApiResult> GetYouzanToken(string grant_id)
    {
        var res = new ApiResult(StatusCode.Fail);
        //查询缓存是否存在token
        string token = await _cache.GetAsync<string>(CacheKey.cacheYuzanToken + grant_id);
        //缓存中存在token
        if (!token.IsEmpty())
        {
            res.Data = token;
            res.Code = (int)StatusCode.Ok;
            return res;
        }
        else
        {
            var param = new Dictionary<string, object>
                {
                    { "client_id", _options.app_id },
                    { "client_secret", _options.app_secret },
                    { "authorize_type", _options.AuthorizeType },
                    { "grant_id", grant_id},
                    { "refresh", _options.Refresh }
                };

            var post_url = _options.service_url + "/auth/token";
            string req = await BPM.Helpers.Web.Client().Post(post_url).ContentType(BPM.Http.Clients.HttpContentType.Json).Data(param).ResultAsync();
            var response = req.ToObject<YouzanResult<TokenResponse>>();
            if (response.success)
            {
                var result = response.data;
                //Token存储缓存
                await _cache.AddAsync<string>(CacheKey.cacheYuzanToken + grant_id, result.access_token, TimeSpan.FromDays(7));
                res.Code = (int)StatusCode.Ok;
                res.Data = result.access_token;
                return res;
            }
            else
                return new ApiResult(StatusCode.Fail, response.message);
        }
    }

    /// <summary>
    /// 获取有赞数据
    /// </summary>
    /// <returns></returns>
    public async Task<YouzanResult<object>> getYouzanData(YouzanParameter param)
    {
        var grant_id = param.grant_id.IsNullOrEmpty() ? _options.grant_id : param.grant_id;
        var res = await GetYouzanToken(grant_id);
        if (res.Code == (int)StatusCode.Ok)
            youzanToken = res.Data;
        this_goto:
        // 如果参数中提供了access_token，则优先使用
        var tokenToUse = !string.IsNullOrEmpty(param.access_token) ? param.access_token : youzanToken;
        var url = $"{_options.service_url + param.url}?access_token={tokenToUse}";
        string resultString = await Helpers.Web.Client().Post(url).JsonData(param.body).ResultAsync();

        WriteLog("有赞请求接口", url, param.body.ToJson(), resultString);

        var obj = resultString.ToObject<YouzanResult<object>>();
        if (obj.gw_err_resp != null)
        {
            // Token 过期重新获取 (仅当使用系统Token时)
            if (obj.gw_err_resp.err_code == "4203" && string.IsNullOrEmpty(param.access_token))
            {
                // 清除 Token缓存
                await _cache.RemoveAsync(CacheKey.cacheYuzanToken + grant_id);
                // 重新获取token
                res = await GetYouzanToken(grant_id);
                if (res.Code == (int)StatusCode.Ok)
                    youzanToken = res.Data;
                else
                {
                    return new YouzanResult<object>()
                    {
                        success = false,
                        message = res.info
                    };
                }
                goto this_goto;
            }
            var result = new YouzanResult<object>();
            result.code = obj.gw_err_resp.err_code;
            result.message = obj.gw_err_resp.err_msg;
            return result;
        }
        return resultString.ToObject<YouzanResult<object>>();
    }

    /// <summary>
    /// 日志记录
    /// </summary>
    /// <param name="title">标题</param>
    /// <param name="pay_way">支付网关</param>
    /// <param name="requestData">请求数据</param>
    /// <param name="rawData">原始响应</param>
    private void WriteLog(string title, string pay_way, string requestData, string resultData)
    {
        var content = new StringBuilder();
        content.AppendLine($"请求地址:{pay_way}");
        content.AppendLine($"请求参数:{requestData}");
        content.AppendLine($"返回结果:{resultData}");
        _log.Set<LogContent>(p => p.Class = GetType().FullName)
            .Set<LogContent>(p => p.Caption = title)
            .Set<LogContent>(p => p.Content = content)
            .Info();
    }
}