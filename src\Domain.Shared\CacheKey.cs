﻿namespace Domain.Shared
{
    /// <summary>
    /// 缓存键
    /// </summary>
    public static class CacheKey
    {
        /// <summary>
        /// token缓存键
        /// </summary>
        public static readonly string cacheToken = "youzan_";


        /// <summary>
        /// token缓存键
        /// </summary>
        public static readonly string cacheYuzanToken = "youzan_";

        /// <summary>
        /// token缓存键
        /// </summary>
        public static readonly string cacheCardToken = "card_";

        /// <summary>
        /// 权益模板表缓存键
        /// </summary>
        public static readonly string cacheGradeTemplate = "grade_template_";
    }
}
