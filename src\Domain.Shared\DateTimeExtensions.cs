﻿using System;

namespace Domain.Shared;

public class DateTimeExtensions
{
    /// <summary>
    /// long 转换成日期
    /// </summary>
    /// <param name="d"></param>
    /// <returns></returns>
    public static DateTime ConvertLongToDateTime(long d)
    {
        DateTime dtStart = TimeZone.CurrentTimeZone.ToLocalTime(new DateTime(1970, 1, 1));
        long lTime = long.Parse(d + "0000");
        TimeSpan toNow = new TimeSpan(lTime);
        DateTime dtResult = dtStart.Add(toNow);
        return dtResult;
    }
    public static long ConvertDateTimeToLong(DateTime dt)
    {
        DateTime dtStart = TimeZone.CurrentTimeZone.ToLocalTime(new DateTime(1970, 1, 1));
        TimeSpan toNow = dt.Subtract(dtStart);
        long timeStamp = toNow.Ticks;
        timeStamp = long.Parse(timeStamp.ToString().Substring(0, timeStamp.ToString().Length - 4));
        return timeStamp;
    }
    /// <summary>
    /// 两日期相差天数
    /// </summary>
    /// <param name="DateTime1"></param>
    /// <param name="DateTime2"></param>
    /// <returns></returns>
    public static int DateDiff(DateTime DateTime1, DateTime DateTime2)
    {
        TimeSpan ts1 = new TimeSpan(DateTime1.Ticks);
        TimeSpan ts2 = new TimeSpan(DateTime2.Ticks);
        TimeSpan ts = ts1.Subtract(ts2).Duration();
        return ts.Days;
    }

    /// <summary>
    /// 两日期相差秒
    /// </summary>
    /// <param name="DateTime1"></param>
    /// <param name="DateTime2"></param>
    /// <returns></returns>
    public static int DateDiffSeconds(DateTime DateTime1, DateTime DateTime2)
    {
        TimeSpan ts1 = new TimeSpan(DateTime1.Ticks);
        TimeSpan ts2 = new TimeSpan(DateTime2.Ticks);
        TimeSpan ts = ts1.Subtract(ts2).Duration();
        return Convert.ToInt32(ts.TotalSeconds);
    }
    public static string SecondsDiff(int second)
    {
        string date_str = string.Empty;
        int day = Convert.ToInt32(second / (60 * 60 * 24));
        int hou = Convert.ToInt32(second % (60 * 60 * 24) / 3600);
        int min = Convert.ToInt32(second % (60 * 60 * 24) % 3600 / 60);
        int sec = Convert.ToInt32(second % (60 * 60 * 24) % 3600 % 60);
        date_str = $"{day}天 ";
        date_str += hou < 10 ? $"0{hou}小时" : $"{hou}小时";
        date_str += min < 10 ? $"0{min}分" : $"{min}分";
        date_str += sec < 10 ? $"0{sec}秒" : $"{sec}秒";
        return date_str;
    }
}