﻿using BPM.Utils.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading;

namespace Domain.Shared.Encrypt
{
    /// <summary>
    /// 应用服务接口签名
    /// </summary>
    public class AppSignHelper
    {
        #region 创建8位随机字符串
        /// <summary>
        /// 创建8位随机字符串
        /// </summary>
        /// <returns></returns>
        public static string CreateNoncestr()
        {
            string chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
            string res = "";
            Random rd = new Random();
            for (int i = 0; i < 8; i++)
            {
                res += chars[rd.Next(chars.Length - 1)];
            }
            return res;
        }
        #endregion

        #region 参数组合
        private static string FormatBizQueryParaMapForUnifiedPay(string json)
        {
            string buff = "";
            try
            {
                Dictionary<string, string> paraMap = json.ToObject<Dictionary<string, string>>();
                var result = from pair in paraMap orderby pair.Key select pair;
                foreach (KeyValuePair<string, string> pair in result)
                {
                    if (pair.Key != "" && pair.Value != "" && pair.Key != "sign")
                    {
                        string key = pair.Key;
                        string val = pair.Value;
                        buff += key + "=" + val + "&";
                    }
                }

                if (buff.Length == 0 == false)
                {
                    buff = buff.Substring(0, buff.Length - 1 - 0);
                }
            }
            catch (Exception e)
            {

            }
            return buff;
        }
        private static string FormatBizQueryParaMapForUnifiedPay(Dictionary<string, object> paraMap)
        {
            string buff = "";
            try
            {
                var result = from pair in paraMap orderby pair.Key select pair;
                foreach (KeyValuePair<string, object> pair in result)
                {
                    if (pair.Key != "" && pair.Value.ToString() != "" && pair.Key != "sign")
                    {
                        string key = pair.Key;
                        string val = pair.Value.ToString();
                        buff += key + "=" + val + "&";
                    }
                }

                if (buff.Length == 0 == false)
                {
                    buff = buff.Substring(0, buff.Length - 1 - 0);
                }
            }
            catch (Exception e)
            {

            }
            return buff;
        }
        #endregion

        #region 签名
        /// <summary>
        /// 参数签名
        /// </summary>
        /// <param name="bizObj">参与签名的参数</param>
        /// <param name="appkey">应用密钥</param>
        /// <returns></returns>
        public static string GetSign(string json, string appkey)
        {
            string unSignParaString = FormatBizQueryParaMapForUnifiedPay(json);
            return MD5Sign.Md5Sign(unSignParaString, appkey);
        }
        /// <summary>
        /// 参数签名
        /// </summary>
        /// <param name="bizObj">参与签名的参数</param>
        /// <param name="appkey">应用密钥</param>
        /// <returns></returns>
        public static string GetSign(Dictionary<string, object> bizObj, string appkey)
        {
            string unSignParaString = FormatBizQueryParaMapForUnifiedPay(bizObj);
            return MD5Sign.Md5Sign(unSignParaString, appkey);
        }
        #endregion

        /// <summary>
        /// Hash 签名
        /// </summary>
        /// <param name="s"></param>
        /// <returns></returns>
        public static string GetHashSign(string s)
        {
            //建立SHA1对象

            SHA1 sha = new SHA1CryptoServiceProvider();
            //将mystr转换成byte[]
            UTF8Encoding enc = new UTF8Encoding();
            byte[] dataToHash = enc.GetBytes(s);
            //Hash运算
            byte[] dataHashed = sha.ComputeHash(dataToHash);
            //将运算结果转换成string
            return BitConverter.ToString(dataHashed).Replace("-", "");
        }

        /// <summary>
        /// 生成单据号
        /// </summary>
        /// <returns></returns>
        public static string GenerateId(string prefix = "")
        {
            StringBuilder sb = new StringBuilder();
            string text = string.Empty;
            var random = new Random(Guid.NewGuid().GetHashCode());
            for (int i = 0; i < 7; i++)
            {
                int num = random.Next();
                text += ((char)(48 + (ushort)(num % 10))).ToString();
            }
            sb.Append(DateTime.Now.ToString("MMdd") + text);
            Thread.Sleep(15);
            return prefix + sb.ToString();
        }

        #region 根据GUID获取唯一数字序列_64位
        /// <summary>
        /// 根据GUID获取唯一数字序列
        /// </summary>
        public static string GuidToInt64()
        {
            byte[] bytes = Guid.NewGuid().ToByteArray();
            return string.Format("{0:yyyyMMdd}", DateTime.Now) + BitConverter.ToInt64(bytes, 0).ToString();
        }
        #endregion

    }
}
