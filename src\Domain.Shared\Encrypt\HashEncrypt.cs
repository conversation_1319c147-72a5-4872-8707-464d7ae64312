﻿using System;
using System.Security.Cryptography;
using System.Text;

namespace Domain.Shared.Encrypt
{

    /// <summary>
    /// 版 本 BPM敏捷开发框架
    /// Copyright (c) 2005-2018 
    /// 创建人：Aarons
    /// 日 期：2022.02.14
    /// 描 述：Hash算法加密
    /// </summary>
    public class HashEncrypt
    {

        /// <summary>
        /// 密码加密
        /// </summary>
        /// <param name="pass">密码</param>
        /// <param name="salt">规则</param>
        /// <returns></returns>
        public static string EncodePassword(string pass, string salt)
        {
            try
            {
                byte[] bytes = Encoding.Unicode.GetBytes(pass);
                byte[] src = Convert.FromBase64String(salt);
                byte[] dst = new byte[src.Length + bytes.Length];
                byte[] inArray = null;
                Buffer.BlockCopy(src, 0, dst, 0, src.Length);
                Buffer.BlockCopy(bytes, 0, dst, src.Length, bytes.Length);
                inArray = HashAlgorithm.Create("SHA1").ComputeHash(dst);
                string password = Convert.ToBase64String(inArray);
                return Convert.ToBase64String(inArray);
            }
            catch (Exception ex)
            {

                throw;
            }
        }
    }
}
