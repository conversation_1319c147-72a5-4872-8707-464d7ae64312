﻿using System;
using System.Security.Cryptography;
using System.Text;

namespace Domain.Shared.Encrypt
{


    /// <summary>
    /// Md5签名
    /// </summary>
    public class MD5Sign
    {
        public static string Md5Sign(string content, string key)
        {
            string signStr = "";

            if (key == "") return null;

            if (content == "") return null;

            signStr = content + "&key=" + key;

            return MD5(signStr).ToUpper();
        }
        /// <summary>
        /// 24位MD5签名
        /// </summary>
        /// <param name="content"></param>
        /// <param name="key"></param>
        /// <returns></returns>
        public static string Md5Sign24(string content, string key) => Md5Sign(content, key).Substring(0, 24);


        public static string MD5(string s)
        {
            char[] hexDigits = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
                    'A', 'B', 'C', 'D', 'E', 'F' };
            try
            {

                byte[] btInput = Encoding.UTF8.GetBytes(s);
                // 获得MD5摘要算法的 MessageDigest 对象
                MD5 mdInst = System.Security.Cryptography.MD5.Create();
                // 使用指定的字节更新摘要
                mdInst.ComputeHash(btInput);
                // 获得密文
                byte[] md = mdInst.Hash;
                // 把密文转换成十六进制的字符串形式
                int j = md.Length;
                char[] str = new char[j * 2];
                int k = 0;
                for (int i = 0; i < j; i++)
                {
                    byte byte0 = md[i];
                    str[k++] = hexDigits[byte0 >> 4 & 0xf];
                    str[k++] = hexDigits[byte0 & 0xf];
                }
                return new string(str);
            }
            catch (Exception e)
            {
                Console.Error.WriteLine(e.StackTrace);
                return null;
            }
        }
        /// <summary>
        /// 哈希签名
        /// </summary>
        /// <param name="s"></param>
        /// <returns></returns>
        public static string HashSignature(string s)
        {
            //建立SHA1对象

            SHA1 sha = new SHA1CryptoServiceProvider();
            //将mystr转换成byte[]
            UTF8Encoding enc = new UTF8Encoding();
            byte[] dataToHash = enc.GetBytes(s);
            //Hash运算
            byte[] dataHashed = sha.ComputeHash(dataToHash);
            //将运算结果转换成string
            string hash = BitConverter.ToString(dataHashed).Replace("-", "");
            return hash;
        }
    }
}
