﻿using BPM.Events;
using BPM.Events.Messages;
using System;

namespace Domain.Events
{
    public class baseLogEvent : MessageEvent<baseLogMessage>
    {

        /// <summary>
        /// 初始化一个<see cref="baseLogEvent"/>类型的实例
        /// </summary>
        /// <param name="data">数据</param>
        public baseLogEvent(baseLogMessage data) : base(data)
        {
            Send = false;
            Name = "logEvent";
        }

    }

    /// <summary>
    /// 用户登录消息
    /// </summary>
    public class baseLogMessage : IEventSession
    {
        /// <summary>
        /// 分类Id 1-登陆2-访问3-操作4-异常
        /// </summary>
        /// <returns></returns>
        public int? F_CategoryId { get; set; }
        /// <summary>
        /// 来源对象主键
        /// </summary>
        /// <returns></returns>
        public string F_SourceObjectId { get; set; }
        /// <summary>
        /// 来源日志内容
        /// </summary>
        /// <returns></returns>
        public string F_SourceContentJson { get; set; }
        /// <summary>
        /// 操作用户Id
        /// </summary>
        /// <returns></returns>
        public string F_OperateUserId { get; set; }
        /// <summary>
        /// 操作用户
        /// </summary>
        /// <returns></returns>
        public string F_OperateAccount { get; set; }
        /// <summary>
        /// 操作类型
        /// </summary>
        /// <returns></returns>
        public string F_OperateType { get; set; }
        /// <summary>
        /// 系统功能
        /// </summary>
        /// <returns></returns>
        public string F_Module { get; set; }
        /// <summary>
        /// IP地址
        /// </summary>
        /// <returns></returns>
        public string F_IPAddress { get; set; }
        /// <summary>
        /// 浏览器
        /// </summary>
        /// <returns></returns>
        public string F_Browser { get; set; }

        /// <summary>
        /// 执行结果状态
        /// </summary>
        /// <returns></returns>
        public int? F_ExecuteResult { get; set; }
        /// <summary>
        /// 执行结果信息
        /// </summary>
        /// <returns></returns>
        public string F_ExecuteResultJson { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        /// <returns></returns>
        public string F_Description { get; set; }  
        /// <summary>
        /// 会话标识
        /// </summary>
        public string SessionId { get; set; }
    }
}
