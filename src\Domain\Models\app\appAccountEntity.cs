﻿using System;

namespace Domain.Models.app

{    /// <summary> 
     /// 版 本 BPM敏捷开发框架 
     /// Copyright (c) 2005-2018  
     /// 创 建：超级管理员 
     /// 日 期：2021-08-15 11:12:47.013
     /// 描 述：API接口应用 
     /// </summary> 
    public class appAccountEntity
    {
        #region 实体成员 
        /// <summary> 
        /// 主键ID（自增） 
        /// </summary> 
        /// <returns></returns> 
        public string Id { get; set; }
        /// <summary> 
        /// 开发者ID(AppID) 
        /// </summary> 
        /// <returns></returns> 
        public string app_id { get; set; }
        /// <summary> 
        /// 开发者密码(AppSecret)
        /// 开发者密码是校验公众号开发者身份的密码，具有极高的安全性。切记勿把密码直接交给第三方开发者或直接存储在代码中。
        /// </summary> 
        /// <returns></returns> 
        public string app_secret { get; set; }
        /// <summary> 
        /// 开发者签名密钥 
        /// </summary> 
        /// <returns></returns> 
        public string sign_key { get; set; }
        /// <summary>
        /// 应用类型
        /// </summary>
        public string app_type { get; set; }
        /// <summary> 
        /// 应用状态(固定值： 0-关闭、1.正常) 
        /// </summary> 
        /// <returns></returns> 
        public int status { get; set; }
        /// <summary> 
        /// 应用描述
        /// </summary> 
        /// <returns></returns> 
        public string dsc { get; set; }
        /// <summary> 
        /// 创建用户 
        /// </summary> 
        /// <returns></returns> 
        public string created_user_id { get; set; }
        /// <summary> 
        /// 创建日期 
        /// </summary> 
        /// <returns></returns> 
        public DateTime? created_date { get; set; }
        /// <summary> 
        /// 修改人 
        /// </summary> 
        /// <returns></returns> 
        public string modify_user_id { get; set; }
        /// <summary> 
        /// 修改日期 
        /// </summary> 
        /// <returns></returns> 
        public DateTime? modify_date { get; set; }
        #endregion

        #region 扩展操作 
        /// <summary> 
        /// 新增调用 
        /// </summary> 
        public void Create()
        {
            this.Id = Guid.NewGuid().ToString();
            this.created_date = DateTime.Now;
            this.modify_date = DateTime.Now;
        }
        #endregion
    }
}