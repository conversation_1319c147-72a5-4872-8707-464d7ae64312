﻿using BPM.Extensions;
using BPM.Security.Claims;
using System;
using System.Collections.Generic;
using System.Security.Claims;

namespace Domain
{
    /// <summary>
    /// 用户
    /// </summary>
    public partial class baseUser
    {

        /// <summary>
        /// 初始化一个<see cref="User"/>类型的实例
        /// </summary>
        /// <param name="id">用户标识</param>
        public baseUser()
        {
            _claims = new List<Claim>();
        }

        /// <summary>
        /// 应用Id
        /// </summary>
        public string AppId { get; set; }

        /// <summary>
        /// 应用名称
        /// </summary>
        public string AppName { get; set; }

        /// <summary>
        /// 用户Id
        /// </summary>
        public string UserId { get; set; }

        /// <summary>
        /// 用户名称
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// 租户编号
        /// </summary>
        public string TenantId { get; set; }

        /// <summary>
        /// 租户名称
        /// </summary>
        public string TenantName { get; set; }


        /// <summary>
        /// 声明列表
        /// </summary>
        private readonly List<Claim> _claims;

        /// <summary>
        /// 获取声明列表
        /// </summary>
        public List<Claim> GetClaims() => _claims;

        /// <summary>
        /// 添加声明
        /// </summary>
        /// <param name="claim">声明</param>
        public void AddClaim(Claim claim)
        {
            if (claim == null
                || claim.Value.IsEmpty()
                || _claims.Exists(x => string.Equals(x.Type.SafeString(), claim.Type.SafeString(), StringComparison.CurrentCultureIgnoreCase)))
                return;
            _claims.Add(claim);
        }

        /// <summary>
        /// 添加声明
        /// </summary>
        /// <param name="type">类型</param>
        /// <param name="value">值</param>
        public void AddClaim(string type, string value)
        {
            if (type.IsEmpty() || value.IsEmpty())
                return;
            AddClaim(new Claim(type, value));
        }

        /// <summary>
        /// 添加用户声明
        /// </summary>
        public void AddUserClaims()
        {
            AddClaim(BPMClaimTypes.UserId, UserId);
            AddClaim(BPMClaimTypes.UserName, UserName);
            AddClaim(BPMClaimTypes.ApplicationId, AppId);
            AddClaim(BPMClaimTypes.ApplicationName, AppName);
            AddClaim(BPMClaimTypes.TenantId, TenantId);
            AddClaim(BPMClaimTypes.TenantName, TenantName);
        }
    }
}
