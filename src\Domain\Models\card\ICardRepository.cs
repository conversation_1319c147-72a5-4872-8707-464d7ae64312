﻿using BPM.Domain.Repositories;
using Domain.Models.card;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Domain.card.Repository
{

    /// <summary>
    /// 版 本 BPM敏捷开发框架
    
    /// 创建人：Aarons
    /// 日 期：2022-07-09
    /// 描 述：订单仓储接口
    /// </summary>
    public interface ICardRepository : IRepository<cardEntity, string>
    {

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        Task<List<cardFaceEntity>> getCardfaceList();

        /// <summary>
        /// 
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        Task<cardVFEntity> getCardVfInfo(string code);

        /// <summary>
        /// 保存储值卡信息
        /// </summary>
        /// <param name="entity"></param>
        /// <param name="cardAdmTxns"></param>
        /// <returns></returns>
        Task saveCard(cardEntity entity, cardAdmTxnsEntity cardAdmTxns);

        /// <summary>
        /// 保存交易记录
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        Task savePayTradeLog(cardEntity entity, cardTxnsEntity cardTxns);
    }
}
