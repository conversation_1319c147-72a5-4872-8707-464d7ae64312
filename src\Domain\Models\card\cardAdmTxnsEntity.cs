﻿using System;

namespace Domain.Models.card;

/// <summary>
/// 业务记录表
/// </summary>
public class cardAdmTxnsEntity
{
    /// <summary>
    /// 终端id
    /// </summary>
    public string TERMINALID { get; set; }
    /// <summary>
    /// 操作时间
    /// </summary>
    public DateTime OPDATE { get; set; }
    /// <summary>
    /// 操作人
    /// </summary>
    public string OPERATOR { get; set; }
    /// <summary>
    /// 卡号
    /// </summary>
    public string CARDNUM { get; set; }
    /// <summary>
    /// 操作类型
    /// </summary>
    public int OPERATION { get; set; }
    /// <summary>
    /// 扣款前金额
    /// </summary>
    public decimal? ORGVALUE { get; set; }
    /// <summary>
    /// 金额
    /// </summary>
    public decimal? OPVALUE { get; set; }
    /// <summary>
    /// 状态
    /// </summary>
    public int STATUS { get; set; }
    /// <summary>
    /// 交易单号
    /// </summary>
    public string REFNUM { get; set; }
    /// <summary>
    /// 交易流水
    /// </summary>
    public string SERIALNO { get; set; }
    /// <summary>
    /// 交易流水号
    /// </summary>
    public int VNUM { get; set; }
    /// <summary>
    /// 备注
    /// </summary>
    public string REMARKS { get; set; }

    public void Init()
    {
        this.OPERATOR = "iPosApi";
        this.OPDATE = DateTime.Now;
        this.OPERATION = 28;
    }
}
