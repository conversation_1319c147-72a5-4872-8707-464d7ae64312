﻿using BPM.Domain.Entities;
using System;

namespace Domain.Models.card
{
    /// <summary>
    /// 储值卡信息表
    /// </summary>
    public class cardEntity : AggregateRoot<cardEntity, string>
    {
        /// <summary>
        /// 卡面号
        /// </summary>
        public string SHORTNAME { get; set; }
        /// <summary>
        /// 创建日期
        /// </summary>
        public DateTime? CREDATE { get; set; }
        /// <summary>
        /// 激活日期
        /// </summary>
        public DateTime? ISSDATE { get; set; }
        /// <summary>
        /// 最后登录日期
        /// </summary>
        public DateTime? LSTDATE { get; set; }
        /// <summary>
        /// 有效期
        /// </summary>
        public DateTime? EXPDATE { get; set; }
        /// <summary>
        /// 是否激活
        /// </summary>
        public bool ISACTIVE { get; set; }
        /// <summary>
        /// 面值
        /// </summary>
        public decimal FACE { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public decimal SAVED { get; set; }
        /// <summary>
        /// 余额
        /// </summary>
        public decimal? BALANCE { get; set; }

        /// <summary>
        /// 仓号
        /// </summary>
        public string WAREHOUSE { get; set; }

        public string REFCODE { get; set; }

        /// <summary>
        /// 校验码
        /// </summary>
        public string VFCODE { get; set; }

        public string TYPE { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public int? STATUS { get; set; }

        /// <summary>
        /// 密码
        /// </summary>
        public string PASSWORD { get; set; }

        /// <summary>
        /// 手机号
        /// </summary>
        public string PHONE { get; set; }

        /// <summary>
        /// 操作人
        /// </summary>
        public string OPERATOR { get;set; }


        /// <summary>
        /// 初始化
        /// </summary>
        public void Init(string card_no,string vf_code,string vf_paas_word)
        {
            this.CREDATE = DateTime.Now;
            this.ISSDATE = DateTime.Now;
            this.EXPDATE = DateTime.Now.AddYears(100);
            this.ISACTIVE = true;
            this.SAVED = 0;
            this.TYPE = "B";
            this.STATUS = 1;
            this.OPERATOR = "iPosApi";
            this.Id = card_no;
            this.SHORTNAME=card_no.Substring(0, 13);
            this.REFCODE = card_no.Substring(0, 13);
            this.VFCODE = vf_code;
            this.PASSWORD = vf_paas_word;

        }

    }
}
