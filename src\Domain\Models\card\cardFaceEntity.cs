﻿using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace Domain.Models.card;

/// <summary>
/// 储值卡面值规则表
/// </summary>
public class cardFaceEntity
{
    /// <summary>
    /// 主键
    /// </summary>
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int UID { get; set; }

    /// <summary>
    /// 门店
    /// </summary>

    public string WAREHOUSE { get; set; }

    /// <summary>
    /// 充值面值
    /// </summary>
    public decimal? AMT { get; set; }

    /// <summary>
    /// 实际面值
    /// </summary>
    public decimal? NET { get; set; }


}

