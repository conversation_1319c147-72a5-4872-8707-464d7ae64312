﻿using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace Domain.Models.card
{

    /// <summary>
    /// 储值卡消费日志
    /// </summary>
    public class cardTxnsEntity
    {
        /// <summary>
        /// 自增长
        /// </summary>
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        //public int ID { get; set; }
        public decimal ID { get; set; }
        /// <summary>
        /// 订单单号
        /// </summary>
        public string TXNNUM { get; set; }
        /// <summary>
        /// 交易单号
        /// </summary>
        public string PAYMENT_SN { get; set; }
        /// <summary>
        /// 卡号
        /// </summary>
        public string CARDNUM { get; set; }
        /// <summary>
        /// 仓号
        /// </summary>
        public string SHOP { get; set; }
        /// <summary>
        /// 操作员
        /// </summary>
        public string OPERATOR { get; set; }
        /// <summary>
        /// 机号
        /// </summary>
        public string TERMINALID { get; set; }
        /// <summary>
        /// 交易日期
        /// </summary>
        public DateTime TXNDATE { get; set; }
        /// <summary>
        /// 订单序号
        /// </summary>
        public int VNUM { get; set; }
        /// <summary>
        /// 扣款前金额
        /// </summary>
        public decimal? ORGAMT { get; set; }
        /// <summary>
        /// 扣款金额
        /// </summary>
        public decimal? DEBAMT { get; set; }


    }
}
