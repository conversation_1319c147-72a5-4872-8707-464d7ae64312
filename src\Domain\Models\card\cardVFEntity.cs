﻿using System;

namespace Domain.Models.card;

/// <summary>
/// 制卡表
/// </summary>
public class cardVFEntity
{
    /// <summary>
    /// 号码
    /// </summary>
    public string REFCODE { get; set; }

    /// <summary>
    /// 校验码
    /// </summary>

    public string VFCODE { get; set; }

    /// <summary>
    /// 创建日期
    /// </summary>
    public DateTime CREDATE { get; set; }

    /// <summary>
    /// 卡号
    /// </summary>
    public string CODE { get; set; }

    /// <summary>
    /// 面值
    /// </summary>
    public decimal? FACE { get; set; }

    /// <summary>
    /// 密码
    /// </summary>
    public string PASSWORD { get; set; }

}

