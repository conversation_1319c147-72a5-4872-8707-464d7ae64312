using System;

namespace Domain.Models.member
{
    /// <summary>
    /// 会员黑名单实体
    /// </summary>
    public class memberBlacklistEntity
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        public long id { get; set; }

        /// <summary>
        /// 会员号码（手机号或卡号）
        /// </summary>
        public string CUSTOMER { get; set; }

        /// <summary>
        /// 手机号
        /// </summary>
        public string phone { get; set; }

        /// <summary>
        /// 会员状态：
        /// 0: 正常状态，不需要动态码
        /// 1: 黑名单，需要动态码验证
        /// 2: 风险会员，需要动态码验证
        /// 3: 异常会员，需要动态码验证
        /// 4: 受限会员，需要动态码验证
        /// 5: 临时禁用，无法使用
        /// 6: 永久禁用，无法使用
        /// </summary>
        public int status { get; set; }

        /// <summary>
        /// 原因
        /// </summary>
        public string reason { get; set; }

        /// <summary>
        /// 操作人
        /// </summary>
        public string @operator { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? create_time { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? update_time { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string remark { get; set; }
    }
} 