﻿using System;

namespace Domain.Models.member
{
    /// <summary>
    /// 会员实体
    /// </summary>
    public class memberEntity
    {
        /// <summary>
        /// 会员号
        /// </summary>
        public string CODE { get; set; }
        /// <summary>
        /// 会员名称
        /// </summary>
        public string MEMBER_NAME { get; set; }
        /// <summary>
        /// 有效期
        /// </summary>
        public DateTime EXPDATE { get; set; }
        /// <summary>
        /// 等级
        /// </summary>
        public string GRADE { get; set; }
        public decimal VAMT { get; set; }
        /// <summary>
        /// 积分
        /// </summary>
        public int CPTS { get; set; }
        public decimal CREDITLIMIT { get; set; }
        public decimal USEDOD { get; set; }
        /// <summary>
        /// 手机号
        /// </summary>
        public string PHONE { get; set; }
        /// <summary>
        /// 生日
        /// </summary>
        public DateTime? BIRTHDAY { get; set; }
        /// <summary>
        /// 折扣
        /// </summary>
        public decimal DISC { get; set; }
        /// <summary>
        /// 生日折扣
        /// </summary>
        public decimal BIRTHDAY_DISC { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public int STATUS { get; set; }

    }
}
