﻿namespace Domain.Models.member
{
    /// <summary>
    /// 会员等级
    /// </summary>
    public class memberGradeEntity
    {
        /// <summary>
        /// 等级编号
        /// </summary>
        public string GRADE { get; set; }

        public decimal TTLAMT1 { get; set; }

        public decimal TTLAMT2 { get; set; }

        public int VPT { get; set; }

        public decimal DISCOUNT { get; set; }
        /// <summary>
        /// 描述
        /// </summary>
        public string DESCRIPT { get; set; }

    }

    /// <summary>
    /// 会员等级
    /// </summary>
    public class memberLzGradeEntity
    {
        /// <summary>
        /// 等级编号
        /// </summary>
       public string Grade { get; set; }

        /// <summary>
        /// 灵智会员等级编号
        /// </summary>
        public string lzCard { get; set; }

        /// <summary>
        /// 折扣
        /// </summary>
        public decimal DISCOUNT { get; set; }

        /// <summary>
        /// 折扣描述
        /// </summary>
        public string DESCRIPT { get; set; }

    }
}
