﻿using BPM.Domain.Repositories;
using Domain.Models.order;
using Domain.Models.pay;
using Domain.Models.sync;
using Domain.Models.user;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Domain.order.Repository;


/// <summary>
/// 版 本 BPM敏捷开发框架

/// 创建人：Aarons
/// 日 期：2022-07-09
/// 描 述：订单仓储接口
/// </summary>
public interface IOrderRepository : IRepository<shopTxnsEntity, string>
{

    /// <summary>
    /// 获取销售记录信息
    /// </summary>
    /// <param name="txnnum"></param>
    /// <returns></returns>
    Task<List<shopTxnsEntity>> getShopTxnsList(string txnnum);

    /// <summary>
    /// 保存交易记录
    /// </summary>
    /// <param name="entity"></param>
    /// <returns></returns>
    Task savePayTradeLog(payTradeLogEntity entity);

    /// <summary>
    /// 保存用户操作日志
    /// </summary>
    /// <param name="entity"></param>
    /// <returns></returns>
    Task saveUserLogs(List<userLogsEntity> list);

    /// <summary>
    /// 获取订单日志记录
    /// </summary>
    /// <param name="shop_id"></param>
    /// <param name="order_no"></param>
    /// <returns></returns>
    syncLogsEntity getSyncLogs(string shop_id, string order_no);

    /// <summary>
    ///  插入订单同步日志
    /// </summary>
    /// <param name="entity"></param>
    /// <returns></returns>
    Task saveSyncLogs(syncLogsEntity entity);

    /// <summary>
    /// 插入订单明细
    /// </summary>
    /// <param name="list"></param>
    /// <returns></returns>
    Task savePurgingOrder(List<shopTxnsEntity> list);
}
