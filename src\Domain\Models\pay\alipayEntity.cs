﻿namespace Domain.Models.pay
{

    /// <summary>
    /// 版 本 BPM敏捷开发框架
    
    /// 创建人：Aarons
    /// 日 期：2022-06-08
    /// 描 述：支付宝支付
    /// </summary>
    public class alipayEntity:baseEntity
    {
        /// <summary> 
        /// 主键编号 
        /// </summary> 
        /// <returns></returns> 
        public string id { get; set; }
        /// <summary> 
        /// 门店编号
        /// </summary> 
        /// <returns></returns> 
        public string shop_id { get; set; }
        /// <summary> 
        /// 商家PID（服务商模式传入商户分配的PID） 
        /// </summary> 
        /// <returns></returns> 
        public string pid { get; set; }
        /// <summary> 
        /// 应用编号 
        /// </summary> 
        /// <returns></returns> 
        public string alipay_app_id { get; set; }
        /// <summary> 
        /// 客户私钥 
        /// </summary> 
        /// <returns></returns> 
        public string merchant_private_key { get; set; }
        /// <summary> 
        /// merchant_public_key 
        /// </summary> 
        /// <returns></returns> 
        public string merchant_public_key { get; set; }
        /// <summary> 
        /// alipay_public_key 
        /// </summary> 
        /// <returns></returns> 
        public string alipay_public_key { get; set; }

        /// <summary> 
        /// 系统商编号
        /// </summary> 
        /// <returns></returns> 
        public string sys_service_provider_id { get; set; }

        /// <summary> 
        /// 签名加密类型（固定值：1.RSA2、2.RSA） 
        /// </summary> 
        /// <returns></returns> 
        public string sign_type { get; set; }
    }
}
