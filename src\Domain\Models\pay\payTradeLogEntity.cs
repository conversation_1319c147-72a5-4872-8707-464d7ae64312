﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.Models.pay
{

    /// <summary>
    /// 版 本 BPM敏捷开发框架
    
    /// 创建人：Aarons
    /// 日 期：2022.03.21
    /// 描 述：订单交易表
    /// </summary>
    public class payTradeLogEntity
    {
        /// <summary> 
        /// 主键ID
        /// </summary> 
        /// <returns></returns> 
        public string id { get; set; }
        /// <summary> 
        /// 订单编号（odr_order表id） 
        /// </summary> 
        /// <returns></returns> 
        public string order_id { get; set; }
        /// <summary> 
        /// 交易类型（固定值：1.支付、2.退款） 
        /// </summary> 
        /// <returns></returns> 
        public string trade_type { get; set; }
        /// <summary> 
        /// 商户支付单号 
        /// </summary> 
        /// <returns></returns> 
        public string pay_sn { get; set; }
        /// <summary>
        /// 退款单号
        /// </summary>
        public string refund_sn { get; set; }
        /// <summary> 
        /// 商户支付方式 
        /// </summary> 
        /// <returns></returns> 
        public string pay_code { get; set; }
        /// <summary> 
        /// 商户支付金额（元） 
        /// </summary> 
        /// <returns></returns> 
        public decimal? trade_fee { get; set; }
        /// <summary> 
        /// 第三方交易单号 
        /// </summary> 
        /// <returns></returns> 
        public string trade_no { get; set; }
        /// <summary> 
        /// 创建记录时间 
        /// </summary> 
        /// <returns></returns> 
        public DateTime? created_date { get; set; }
        /// <summary> 
        /// 第三方交易时间 
        /// </summary> 
        /// <returns></returns> 
        public DateTime? trade_date { get; set; }
        /// <summary> 
        /// 第三方交易回调数据 
        /// </summary> 
        /// <returns></returns> 
        public string json { get; set; }
        /// <summary> 
        /// qr_code 
        /// </summary> 
        /// <returns></returns> 
        public string qr_code { get; set; }

        /// <summary>
        /// 门店编号
        /// </summary>
        public string shop_id { get; set; }
    }
}
