﻿using System;

namespace Domain.Models.pay
{
    /// <summary>
    /// 支付方式实体
    /// </summary>
    public class paymethodEntity
    {
        /// <summary>
        /// 支付代码
        /// </summary>
        public string CODE { get; set; }

        /// <summary>
        /// 支付名称
        /// </summary>
        public string SHORTNAME { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        public int? EXRATE { get; set; }

        /// <summary>
        /// 终端号
        /// </summary>
        public string TERMINALID { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int? INVOICE { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int? ISINPUTCODE { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int? ISCANDEL { get; set; }


    }
}
