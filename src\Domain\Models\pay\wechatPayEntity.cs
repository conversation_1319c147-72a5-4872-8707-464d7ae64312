﻿namespace Domain.Models.pay
{

    /// <summary>
    /// 版 本 BPM敏捷开发框架
    
    /// 创建人：Aarons
    /// 日 期：2022.03.22
    /// 描 述：微信支付
    /// </summary>
    public class wechatPayEntity : baseEntity
    {
        /// <summary> 
        /// 主键编号 
        /// </summary> 
        /// <returns></returns> 
        public string id { get; set; }
        /// <summary> 
        /// 门店编号（sto_store表id） 
        /// </summary> 
        /// <returns></returns> 
        public string shop_id { get; set; }
        /// <summary> 
        /// 服务商的APPID(微信支付分配的公众账号ID（企业号corpid即为此appId）) 
        /// </summary> 
        /// <returns></returns> 
        public string app_id { get; set; }
        /// <summary> 
        /// 商户号(微信支付分配的商户号) 
        /// </summary> 
        /// <returns></returns> 
        public string mch_id { get; set; }
        /// <summary> 
        /// 子商户公众账号ID(微信分配的子商户公众账号ID，如需在支付完成后获取sub_openid则此参数必传。) 
        /// </summary> 
        /// <returns></returns> 
        public string sub_appid { get; set; }
        /// <summary> 
        /// 子商户号(微信支付分配的子商户号) 
        /// </summary> 
        /// <returns></returns> 
        public string sub_mch_id { get; set; }
        /// <summary> 
        /// 支付密钥 
        /// </summary> 
        /// <returns></returns> 
        public string pay_key { get; set; }
        /// <summary> 
        /// 证书路径 
        /// </summary> 
        /// <returns></returns> 
        public string cert_path { get; set; }
    }
}
