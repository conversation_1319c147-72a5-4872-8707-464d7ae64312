using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Domain.Models.product
{
    /// <summary>
    /// 商品表
    /// </summary>
    [Table("pdt_product")]
    public class productEntity
    {
        /// <summary>
        /// 商品编号
        /// </summary>
        [Key]
        [Column("product_id")]
        public string product_id { get; set; }

        /// <summary>
        /// 商品代码
        /// </summary>
        public string product_code { get; set; }

        /// <summary>
        /// 总部商品id
        /// </summary>
        public string root_product_id { get; set; }

        /// <summary>
        /// 门店编号
        /// </summary>
        public string store_id { get; set; }

        /// <summary>
        /// 条码
        /// </summary>
        public string bar_code { get; set; }

        /// <summary>
        /// 商品类型
        /// </summary>
        public string type_id { get; set; }

        /// <summary>
        /// 商品类别（book.图书、product.常规商品、pre_sale.预售商品、gift.赠品）
        /// </summary>
        public string goods_type { get; set; }

        /// <summary>
        /// 商品名称
        /// </summary>
        public string product_name { get; set; }

        /// <summary>
        /// 商品状态（固定值：1.上架、0.下架、2.回收站）
        /// </summary>
        public int? state { get; set; }

        /// <summary>
        /// 部类编号
        /// </summary>
        public string dpt_code { get; set; }

        /// <summary>
        /// 品牌编号
        /// </summary>
        public string brand_code { get; set; }

        /// <summary>
        /// 定价
        /// </summary>
        public decimal make_price { get; set; }

        /// <summary>
        /// 是否启用多SKU（0.不启用、1.启用）
        /// </summary>
        public int? has_sku { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        public decimal? inctax { get; set; }

        /// <summary>
        /// 重量
        /// </summary>
        public decimal? weight { get; set; }

        /// <summary>
        /// 商品描述
        /// </summary>
        public string product_des { get; set; }

        /// <summary>
        /// 创建人编号
        /// </summary>
        public string created_user_id { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime created_date { get; set; }

        /// <summary>
        /// 修改人id
        /// </summary>
        public string modify_user_id { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? modify_date { get; set; }

        /// <summary>
        /// 是否同步
        /// </summary>
        [Column("is_sync")]
        [DatabaseGenerated(DatabaseGeneratedOption.Computed)]
        public int? is_sync { get; set; }

        /// <summary>
        /// 类型(select,add,edit)
        /// </summary>
        public string tag_status { get; set; }

        /// <summary>
        /// 店铺渠道类型;0 :网店;1: 门店
        /// </summary>
        public int? channel { get; set; }
    }
} 