﻿using System.ComponentModel.DataAnnotations.Schema;

namespace Domain.Models.product;

/// <summary>
/// 版 本 BPM敏捷开发框架

/// 创建人：Aarons
/// 日 期：2022-05-27
/// 描 述：商品规格实体
/// </summary>
[Table("pdt_sku")]
public class productSkuEntity
{
    /// <summary> 
    /// 商品SKUID 
    /// </summary> 
    /// <returns></returns> 
    public string id { get; set; }
    /// <summary> 
    /// 商品编号（pdt_product表id） 
    /// </summary> 
    /// <returns></returns> 
    public string product_id { get; set; }
    /// <summary> 
    /// 商品SKU编号 
    /// </summary> 
    /// <returns></returns> 
    public string sku_sn { get; set; }
    /// <summary> 
    /// 商品标题 
    /// </summary> 
    /// <returns></returns> 
    public string sku_name { get; set; }
    /// <summary> 
    /// 商品市场价 
    /// </summary> 
    /// <returns></returns> 
    public decimal? make_price { get; set; }
    /// <summary> 
    /// 商品销售价,需要在Sku价格所决定的的区间内 
    /// </summary> 
    /// <returns></returns> 
    public decimal? sale_price { get; set; }
    /// <summary> 
    /// 商品成本价 
    /// </summary> 
    /// <returns></returns> 
    public decimal? cost_price { get; set; }
    /// <summary> 
    /// 商品SKU规格描述, 格式：pText:vText;pText:vText，多个sku之间用逗号分隔，如：颜色:黄色;尺寸:M。pText和vText文本中不可以存在冒号和分号以及逗号 
    /// </summary> 
    /// <returns></returns> 
    public string des { get; set; }
    /// <summary> 
    /// 商品SKU键值,由商品SKU明细的规格值ID按小到大组成的数字,便于检索 
    /// </summary> 
    /// <returns></returns> 
    public string group { get; set; }
    /// <summary> 
    /// 商品SKU状态（固定值：0.正常、1.删除，2-禁用） 
    /// </summary> 
    /// <returns></returns> 
    public int? sku_state { get; set; }
    /// <summary> 
    /// 商品店铺渠道类型;0 :网店;1: 门店
    /// </summary> 
    /// <returns></returns> 
    public int? channel { get; set; }
}