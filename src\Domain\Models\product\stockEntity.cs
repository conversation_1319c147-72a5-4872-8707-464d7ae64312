﻿namespace Domain.Models.product
{
    /// <summary>
    /// 库存对象
    /// </summary>
    public class stockEntity
    {
        /// <summary>
        /// 仓号
        /// </summary>
        public string WAREHOUSE { get; set; }

        /// <summary>
        ///
        /// </summary>
        public string RACK { get; set; }

        /// <summary>
        /// 货号
        /// </summary>
        public string PLUCODE { get; set; }

        /// <summary>
        ///  库存
        /// </summary>
        public int? QTY { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int? CQTY { get; set; }
    }
}
