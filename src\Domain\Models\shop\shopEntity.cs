﻿namespace Domain.Models.shop;

/// <summary>
/// 门店信息
/// 版 本：V3.2
/// 版 权：中畅源科技开发有限公司（https://www.szclouds.com）
/// 作 者：Aarons
/// 日 期：2023-01-05.
/// </summary>
public class shopEntity
{
    /// <summary>
    /// 门店编号
    /// </summary>
    public string id { get; set; }

    /// <summary>
    /// 门店编号
    /// </summary>
    public string store_no { get; set; }

    /// <summary>
    /// 上级门店
    /// </summary>
    public string parent_id { get; set; }
    

    /// <summary>
    /// 门店名称
    /// </summary>
    public string store_name { get; set; }

    /// <summary>
    /// 门店名称
    /// </summary>
    public int? status { get; set; }

    /// <summary>
    /// 省份
    /// </summary>
    public string province { get; set; }

    /// <summary>
    /// 城市
    /// </summary>
    public string city { get; set; }

    /// <summary>
    /// 区县
    /// </summary>
    public string area { get; set; }

    /// <summary>
    /// 详细地址
    /// </summary>
    public string address { get; set; }

    /// <summary>
    /// 精度
    /// </summary>
    public decimal? longitude { get; set; }

    /// <summary>
    /// 纬度
    /// </summary>
    public decimal? latitude { get; set; }

    /// <summary>
    /// 电话
    /// </summary>
    public string tel { get; set; }

    /// <summary>
    /// 邮箱
    /// </summary>
    public string email { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string intro { get; set; }

    /// <summary>
    /// 外部门店id
    /// </summary>
    public string source_no { get; set; }
}

