﻿namespace Domain.Models.shop
{
    /// <summary>
    /// 终端信息表
    /// </summary>
    public class terminalEntity
    {
        public string TERMINAL { get; set; }
        public string SHOPID { get; set; }
        public string SHOPNAME { get; set; }
        public string POSKEY { get; set; }
        public string MAC { get; set; }
        public string SECONDMAC { get; set; }
        public string ADDRESS { get; set; }
        public string PHONE { get; set; }
        public string SQLMODE { get; set; }
        public string TERMTXNSDB { get; set; }
        public string SHOPTXNSDB { get; set; }
        public string CRMDB { get; set; }
        public string CARDDB { get; set; }
        public string HEADERTEXT { get; set; }
        public string FOOTERTEXT { get; set; }
        public string PRINTNAME { get; set; }
        public string PAPELEN { get; set; }
        public string PAPELEFT { get; set; }
        public string NOPRINT { get; set; }
        public string PRN_OPENDRAWER_CMD { get; set; }
        public string REP_OPENDRAWER { get; set; }
        public string PRINTPAGENUM { get; set; }
        public string ISDBLOG { get; set; }
        public string CASHACCT { get; set; }
        public string WXACCT { get; set; }
        public string ZFBACCT { get; set; }
        public string VCACCT { get; set; }
        public string BESTDISC { get; set; }
        public string GIFTBEST { get; set; }
        public string ROUNDOFF { get; set; }
        public string REMARKS { get; set; }
        public string ISUSE { get; set; }
        public string TOKEN { get; set; }
        public string STANDALONE { get; set; }
        public string EXCPREFIX { get; set; }
        public string STARTSCAN { get; set; }
        public string ENDSCAN { get; set; }
        public string REALTIMESTOCK { get; set; }
        public string PRINTPLUCODE { get; set; }
        public string PROGRAMNAME { get; set; }
        public string ISPRINTFP { get; set; }
        public string KEEPLOGDAYS { get; set; }
        public string UPDATEURL { get; set; }
        public string ISUSEGIFT { get; set; }
        public string ISTOPMOST { get; set; }
        public string MERGEPLUQTY { get; set; }
        public string ISPRINTSCPIC { get; set; }
        public string SCPICFILENAME { get; set; }
        public string SCPICMSG { get; set; }
        public string CONFIGKEY1 { get; set; }
        public string CONFIGKEY2 { get; set; }
        public string CONFIGKEY3 { get; set; }
        public string CONFIGKEY4 { get; set; }
        public string CONFIGKEY5 { get; set; }
        public string CONFIGKEY6 { get; set; }
    }
}
