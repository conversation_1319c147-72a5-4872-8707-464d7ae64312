﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.Models.sync
{

    /// <summary>
    /// 版 本 BPM敏捷开发框架
    
    /// 创建人：Aarons
    /// 日 期：2022-07-09
    /// 描 述：订单同步日志
    /// </summary>
    public class syncLogsEntity
    {

        /// <summary>
        /// 主键
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 类型
        /// </summary>
        public string Type { get; set; }    

        /// <summary>
        /// 门店编号
        /// </summary>
        public string ShopId { get; set; }

        /// <summary>
        /// 设备号
        /// </summary>
        public string DeviceId { get; set; }

        /// <summary>
        /// 来源编号
        /// </summary>
        public string SourceId { get; set; }

        /// <summary>
        /// 同步状态
        /// </summary>
        public int SyncStatus { get; set; }

        /// <summary>
        /// 同步时间
        /// </summary>
        public DateTime SyncDate { get; set; }

        /// <summary>
        /// 返回内容
        /// </summary>
        public string  Body { get; set; }
   }
}
