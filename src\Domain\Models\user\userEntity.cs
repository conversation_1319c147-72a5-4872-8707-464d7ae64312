﻿namespace Domain.Models.user
{
    /// <summary>
    /// 用户实体
    /// </summary>
    public class userEntity
    {

        /// <summary>
        /// 账户
        /// </summary>
        public string CODE { get; set; }
        /// <summary>
        /// 名称
        /// </summary>
        public string SHORTNAME { get; set; }
        /// <summary>
        /// 是否主管
        /// </summary>
        public int SUPERVISOR { get; set; }
        /// <summary>
        /// 钱箱
        /// </summary>
        public int CASHBOX { get; set; }
        /// <summary>
        /// 密码
        /// </summary>
        public string PASSWORD { get; set; }                
        /// <summary>
        /// 终端号
        /// </summary>
        public string TERMINALID { get; set; }
        /// <summary>
        /// 折扣
        /// </summary>
        public int DISC { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int ISTH { get; set; }
    }
}
