﻿using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace Domain.Models.user
{
    /// <summary>
    /// 用户操作日志
    /// </summary>
    public class userLogsEntity
    {
        /// <summary>
        /// 主键
        /// </summary>
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int UserLogID { get; set; }
        /// <summary>
        /// 日期
        /// </summary>
        public DateTime LogDate { get; set; }
        /// <summary>
        /// 时间
        /// </summary>
        public string LogTime { get; set; }
        /// <summary>
        /// 电脑名称
        /// </summary>
        public string ComputerName { get; set; }
        /// <summary>
        /// ip 地址
        /// </summary>
        public string IPADDRESS { get; set; }
        /// <summary>
        /// 机号
        /// </summary>
        public string TERMINAL { get; set; }
        /// <summary>
        /// 功能
        /// </summary>
        public string FUNCNAME { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string REMARKS { get; set; }

        /// <summary>
        /// 用户代码
        /// </summary>
        public string UserCode { get; set; }

        /// <summary>
        /// 用户名称
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public short STS { get; set; }

    }
}
