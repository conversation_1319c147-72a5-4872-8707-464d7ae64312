﻿using AppService.Events;
using BPM.Data.Sql;
using BPM.Logs;
using BPM.Utils.Json;
using Domain.order.Repository;
using DotNetCore.CAP;
using Infrastructure.UnitOfWorks;
using System.Threading.Tasks;

namespace EventHandlers;

public class OrderEventHandler : MessageEventHandlerBase
{

    /// <summary>
    /// sql 执行器
    /// </summary>
    protected ISqlExecutor _sqlExecutor;

    /// <summary>
    /// 订单仓储接口
    /// </summary>
    private readonly IOrderRepository _orderRepository;

    /// <summary>
    /// 工作单元
    /// </summary>
    private readonly IBaseUnitOfWork _unitOfWork;


    /// <summary>
    /// 日志服务
    /// </summary>
    private readonly ILog _log;

    public OrderEventHandler(IOrderRepository orderRepository, ISqlExecutor sqlExecutor, IBaseUnitOfWork baseUnitOfWork, ILog log)
    {
        _orderRepository = orderRepository;
        _sqlExecutor = sqlExecutor;
        _unitOfWork = baseUnitOfWork;
        _log = log;

    }

    /// <summary>
    /// 订单事件总线
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [CapSubscribe("orderPurgEvent")]
    public async Task orderEvent(orderMessage message)
    {
        try
        {
            _log.Info($"清机请求参数--->{message.ToJson()}");

            await _sqlExecutor.ExecuteSqlAsync("DELETE SHOPTXNS WHERE TERMINAL=@TERMINAL AND CONVERT(VARCHAR(20),TXNTIME,23)=@TXNTIME", new { TERMINAL = message.TERMINAL, TXNTIME = message.TXNTIME });
            //插入订单
            foreach (var item in message.items)
                item.Init();
            _log.Info($"清机明细集合--->{message.items.ToJson()}");
            await _orderRepository.savePurgingOrder(message.items);
            //工作单元
            await _unitOfWork.CommitAsync();

            _log.Info($"清机成功！ 机号:{message.TERMINAL} 时间:{message.TXNTIME}");

        }
        catch (System.Exception ex)
        {
            _log.Error($"清机失败！ 系统错误:{ex.Message}");
        }
    }


}

