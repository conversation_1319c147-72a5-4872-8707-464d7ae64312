﻿namespace Infrastructure.Cap
{
    /// <summary>
    /// RabbitMq配置.
    /// </summary>
    public class RabbitOptions
    {
        /// <summary>
        /// 用户明细
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// 密码
        /// </summary>
        public string Password { get; set; }

        /// <summary>
        /// 地址
        /// </summary>
        public string HostName { get; set; }

        /// <summary>
        /// 端口
        /// </summary>
        public int Port { get; set; }

        /// <summary>
        /// 虚拟目录
        /// </summary>
        public string VirtualHost { get; set; }

    }
}
