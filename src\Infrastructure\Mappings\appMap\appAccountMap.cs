﻿using Domain.Models.app;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Mappings
{
    /// <summary>
    /// 版 本 CBP敏捷开发框架
    /// Copyright (c) 2005-2018 
    /// 创 建：Even
    /// 日 期：2021-08-15 11:11:22.740
    /// 描 述：API接口应用·映射表名配置
    /// </summary>
    /// 订单明细映射配置
    /// </summary>
    public class appAccountMap : BPM.Datas.EntityFramework.SqlServer.EntityMap<appAccountEntity>
    {
        /// <summary>
        /// 映射表
        /// </summary>
        protected override void MapTable(EntityTypeBuilder<appAccountEntity> builder)
        {
            builder.ToTable("app_account");
        }
    }
}
