﻿using Domain.Models.card;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Mappings
{
    /// <summary>
    /// 版 本 CBP敏捷开发框架
    /// Copyright (c) 2005-2018 
    /// 创 建：Aarons
    /// 日 期：2022-07-05
    /// </summary>
    /// 储值卡映射配置
    /// </summary>
    public class cardMap : BPM.Datas.EntityFramework.SqlServer.AggregateRootMap<cardEntity>
    {
        /// <summary>
        /// 映射表
        /// </summary>
        protected override void MapTable(EntityTypeBuilder<cardEntity> builder)
        {
            //定义表名
            builder.ToTable("SP_CARDMASTER");
            //指定主键
            builder.Property(t => t.Id).HasColumnName("CODE");
        }
    }

    /// <summary>
    /// 储值卡交易日志表
    /// </summary>
    public class cardTxnsMap : BPM.Datas.EntityFramework.SqlServer.EntityMap<cardTxnsEntity>
    {
        /// <summary>
        /// 映射表
        /// </summary>
        protected override void MapTable(EntityTypeBuilder<cardTxnsEntity> builder)
        {
            //定义表名
            builder.ToTable("SP_DEBTXNS");
            //定义主键
            builder.HasKey(x => new { x.ID });
        }
    }


    /// <summary>
    /// 储值制卡表
    /// </summary>
    public class cardVFMap : BPM.Datas.EntityFramework.SqlServer.EntityMap<cardVFEntity>
    {
        /// <summary>
        /// 映射表
        /// </summary>
        protected override void MapTable(EntityTypeBuilder<cardVFEntity> builder)
        {
            //定义表名
            builder.ToTable("SP_CARDVF");
            //定义主键
            builder.HasKey(x => new { x.REFCODE });
        }
    }

    /// <summary>
    /// 充值规则表
    /// </summary>
    public class cardFaceMap : BPM.Datas.EntityFramework.SqlServer.EntityMap<cardFaceEntity>
    {
        /// <summary>
        /// 映射表
        /// </summary>
        protected override void MapTable(EntityTypeBuilder<cardFaceEntity> builder)
        {
            //定义表名
            builder.ToTable("SP_CARDFACE");
            //定义主键
            builder.HasKey(x => new { x.UID });
        }
    }

    /// <summary>
    /// 储值卡操作日志表
    /// </summary>
    public class cardAdmTxnsMap : BPM.Datas.EntityFramework.SqlServer.EntityMap<cardAdmTxnsEntity>
    {
        /// <summary>
        /// 映射表
        /// </summary>
        protected override void MapTable(EntityTypeBuilder<cardAdmTxnsEntity> builder)
        {
            //定义表名
            builder.ToTable("SP_ADMTXNS");
            //定义主键
            builder.HasKey(x => new { x.TERMINALID, });
        }
    }
}
