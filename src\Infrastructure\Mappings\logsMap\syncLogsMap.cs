﻿using Domain.Models.sync;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Mappings
{
    /// <summary>
    /// 版 本 CBP敏捷开发框架
    /// Copyright (c) 2005-2018 
    /// 创 建：Aarons
    /// 日 期：2022-07-05
    /// </summary>
    /// 储值卡映射配置
    /// </summary>
    public class syncLogsMap : BPM.Datas.EntityFramework.SqlServer.EntityMap<syncLogsEntity>
    {
        /// <summary>
        /// 映射表
        /// </summary>
        protected override void MapTable(EntityTypeBuilder<syncLogsEntity> builder)
        {
            //定义表名
            builder.ToTable("SyncLogs");
            //指定主键
            builder.HasKey(t => t.Id);
        }
    }


}
