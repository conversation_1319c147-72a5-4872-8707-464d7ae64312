﻿using Domain.Models.market;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Mappings
{
    /// <summary>
    /// 版 本 CBP敏捷开发框架
    /// Copyright (c) 2005-2018 
    /// 创 建：Aarons
    /// 日 期：2022-07-05
    /// </summary>
    /// 换购映射配置
    /// </summary>
    public class tradeitemsMap : BPM.Datas.EntityFramework.SqlServer.EntityMap<tradeitemsEntity>
    {
        /// <summary>
        /// 映射表
        /// </summary>
        protected override void MapTable(EntityTypeBuilder<tradeitemsEntity> builder)
        {
            //定义表名
            builder.ToTable("TRADEITEMS");
            //定义主键
            builder.HasKey(x => new { x.TRADENUM, x.SHOPID });
        }
    }
}
