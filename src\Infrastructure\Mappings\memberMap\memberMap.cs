﻿using Domain.Models.member;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Mappings
{
    /// <summary>
    /// 版 本 CBP敏捷开发框架
    /// Copyright (c) 2005-2018 
    /// 创 建：Aarons
    /// 日 期：2022-07-05
    /// </summary>
    /// 会员映射配置
    /// </summary>
    public class memberMap : BPM.Datas.EntityFramework.SqlServer.EntityMap<memberEntity>
    {
        /// <summary>
        /// 映射表
        /// </summary>
        protected override void MapTable(EntityTypeBuilder<memberEntity> builder)
        {
            //定义表名
            builder.ToTable("MEMBERINFO");
            //定义主键
            builder.HasKey(x => new { x.CODE });
        }
    }
    /// <summary>
    /// 版 本 CBP敏捷开发框架
    /// Copyright (c) 2005-2018 
    /// 创 建：Aarons
    /// 日 期：2022-07-05
    /// </summary>
    /// 客户映射配置
    /// </summary>
    public class customerMap : BPM.Datas.EntityFramework.SqlServer.EntityMap<customerEntity>
    {
        /// <summary>
        /// 映射表
        /// </summary>
        protected override void MapTable(EntityTypeBuilder<customerEntity> builder)
        {
            //定义表名
            builder.ToTable("CUSTOMER");
            //定义主键
            builder.HasKey(x => new { x.customer_sn });
        }
    }


    public class memberGradeMap : BPM.Datas.EntityFramework.SqlServer.EntityMap<memberGradeEntity>
    {
        /// <summary>
        /// 映射表
        /// </summary>
        protected override void MapTable(EntityTypeBuilder<memberGradeEntity> builder)
        {
            //定义表名
            builder.ToTable("CRM_GRADE");
            //定义主键
            builder.HasKey(x => new { x.GRADE });
        }
    }

    /// <summary>
    /// 灵智会员等级关系映射
    /// </summary>
    public class memberLzGradeMap : BPM.Datas.EntityFramework.SqlServer.EntityMap<memberLzGradeEntity>
    {
        /// <summary>
        /// 映射表
        /// </summary>
        protected override void MapTable(EntityTypeBuilder<memberLzGradeEntity> builder)
        {
            //定义表名
            builder.ToTable("CRM_GRADE2LZ");
            //定义主键
            builder.HasKey(x => new { x.Grade });
        }
    }

    /// <summary>
    /// 客户关系映射
    /// </summary>
    public class customerEquityMap : BPM.Datas.EntityFramework.SqlServer.EntityMap<customerEquityEntity>
    {
        /// <summary>
        /// 映射表
        /// </summary>
        protected override void MapTable(EntityTypeBuilder<customerEquityEntity> builder)
        {
            //定义表名
            builder.ToTable("CUSTOMER_EQUITY");
            //定义主键
            builder.HasKey(x => new { x.id });
        }
    }

    /// <summary>
    /// 会员黑名单映射
    /// </summary>
    public class memberBlacklistMap : BPM.Datas.EntityFramework.SqlServer.EntityMap<memberBlacklistEntity>
    {
        /// <summary>
        /// 映射表
        /// </summary>
        protected override void MapTable(EntityTypeBuilder<memberBlacklistEntity> builder)
        {
            //定义表名
            builder.ToTable("member_blacklist");
            //定义主键
            builder.HasKey(x => new { x.id });
        }
    }
}
