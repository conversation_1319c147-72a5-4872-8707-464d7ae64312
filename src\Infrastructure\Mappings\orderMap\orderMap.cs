﻿using Domain.Models.order;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Mappings
{
    /// <summary>
    /// 版 本 CBP敏捷开发框架
    /// Copyright (c) 2005-2018 
    /// 创 建：Aarons
    /// 日 期：2022-07-05
    /// </summary>
    /// 订单映射配置
    /// </summary>
    public class orderMap : BPM.Datas.EntityFramework.SqlServer.AggregateRootMap<shopTxnsEntity>
    {
        /// <summary>
        /// 映射表
        /// </summary>
        protected override void MapTable(EntityTypeBuilder<shopTxnsEntity> builder)
        {
            //定义表名
            builder.ToTable("SHOPTXNS");
            //指定主键
            builder.HasKey(x => x.TXNID);
        }
    }
}
