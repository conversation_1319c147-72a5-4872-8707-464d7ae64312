﻿using Domain.Models.pay;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Mappings
{
    /// <summary>
    /// 版 本 CBP敏捷开发框架
    /// Copyright (c) 2005-2018 
    /// 创 建：Aarons
    /// 日 期：2022-07-05
    /// </summary>
    /// 交易日志映射配置
    /// </summary>
    public class payTradeLogMap : BPM.Datas.EntityFramework.SqlServer.EntityMap<payTradeLogEntity>
    {
        /// <summary>
        /// 映射表
        /// </summary>
        protected override void MapTable(EntityTypeBuilder<payTradeLogEntity> builder)
        {
            //定义表名
            builder.ToTable("payTradeLog");
            //定义主键
            builder.HasKey(x => new { x.id });
        }
    }
}
