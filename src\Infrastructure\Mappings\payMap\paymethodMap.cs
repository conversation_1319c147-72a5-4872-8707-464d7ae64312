﻿using Domain.Models.pay;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Mappings
{
    /// <summary>
    /// 版 本 CBP敏捷开发框架
    /// Copyright (c) 2005-2018 
    /// 创 建：Aarons
    /// 日 期：2022-07-05
    /// </summary>
    /// 支付方式映射配置
    /// </summary>
    public class paymethodMap : BPM.Datas.EntityFramework.SqlServer.EntityMap<paymethodEntity>
    {
        /// <summary>
        /// 映射表
        /// </summary>
        protected override void MapTable(EntityTypeBuilder<paymethodEntity> builder)
        {
            //定义表名
            builder.ToTable("PAYMETHOD");
            //定义主键
            builder.HasKey(x => new { x.CODE, x.TERMINALID });
        }
    }
}
