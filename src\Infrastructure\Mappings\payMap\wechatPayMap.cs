﻿using Domain.Models.pay;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Mappings.payMap
{

    /// <summary>
    /// 版 本 敏捷开发框架
    
    /// 创 建：Aarons
    /// 日 期：2022-07-10
    /// 描 述：门店微信支付配置
    public class wechatPayMap : BPM.Datas.EntityFramework.SqlServer.EntityMap<wechatPayEntity>
    {
        /// <summary>
        /// 映射表
        /// </summary>
        protected override void MapTable(EntityTypeBuilder<wechatPayEntity> builder)
        {
            builder.ToTable("wechatPayConfig");
            //指定主键
            builder.Property(t => t.id);

        }

    }
}
