﻿using Domain.Models.product;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Mappings
{
    /// <summary>
    /// 版 本 CBP敏捷开发框架
    /// Copyright (c) 2005-2018 
    /// 创 建：Aarons
    /// 日 期：2022-05-31
    /// </summary>
    /// 商品分类映射配置
    /// </summary>
    public class classMap : BPM.Datas.EntityFramework.SqlServer.EntityMap<dispayEntity>
    {
        /// <summary>
        /// 映射表
        /// </summary>
        protected override void MapTable(EntityTypeBuilder<dispayEntity> builder)
        {
            //定义表名
            builder.ToTable("CLASS");
            //定义主键
            builder.HasKey(x => new { x.CLSCODE, x.TERMINALID });
        }
    }
}
