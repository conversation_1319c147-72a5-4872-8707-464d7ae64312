﻿using Domain.Models.product;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Mappings
{
    /// <summary>
    /// 版 本 CBP敏捷开发框架
    /// Copyright (c) 2005-2018 
    /// 创 建：Aarons
    /// 日 期：2022-07-05
    /// </summary>
    /// 商品代收映射配置
    /// </summary>
    public class raMap : BPM.Datas.EntityFramework.SqlServer.EntityMap<raEntity>
    {
        /// <summary>
        /// 映射表
        /// </summary>
        protected override void MapTable(EntityTypeBuilder<raEntity> builder)
        {
            //定义表名
            builder.ToTable("RA");
            //定义主键
            builder.HasKey(x => new { x.RACODE, x.TERMINALID });
        }
    }
}
