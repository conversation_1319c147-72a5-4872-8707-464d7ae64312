﻿using Domain.Models.user;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Mappings
{
    /// <summary>
    /// 版 本 CBP敏捷开发框架
    /// Copyright (c) 2005-2018 
    /// 创 建：Aarons
    /// 日 期：2022-05-31
    /// </summary>
    /// 用户日志映射配置
    /// </summary>
    public class userLogsMap : BPM.Datas.EntityFramework.SqlServer.EntityMap<userLogsEntity>
    {
        /// <summary>
        /// 映射表
        /// </summary>
        protected override void MapTable(EntityTypeBuilder<userLogsEntity> builder)
        {
            //定义表名
            builder.ToTable("UserLog_tab");
            //定义主键
            builder.HasKey(x => new { x.UserLogID });
        }
    }
}
