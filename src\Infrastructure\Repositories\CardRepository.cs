﻿using BPM.Datas.EntityFramework.Core;
using BPM.Uow;
using Domain.card.Repository;
using Domain.Models.card;
using Infrastructure.UnitOfWorks;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Security.Cryptography.X509Certificates;
using System.Threading.Tasks;

namespace Infrastructure.Repositories
{
    /// <summary>
    /// 版 本 BPM敏捷开发框架
    
    /// 创建人：Aarons
    /// 日 期：2022-09-05
    /// 描 述：储值卡仓储
    /// </summary>
    public class CardRepository : RepositoryBase<cardEntity, string>, ICardRepository
    {
        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="unitOfWork"></param>
        public CardRepository(ICardUnitOfWork unitOfWork) : base(unitOfWork)
        {



        }

        public Task<List<cardFaceEntity>> getCardfaceList()
        {
            return UnitOfWork.Set<cardFaceEntity>().ToListAsync();
        }

        public async Task<cardVFEntity> getCardVfInfo(string code)
        {
            var info = await UnitOfWork.Set<cardVFEntity>().FirstOrDefaultAsync(x => x.CODE.Equals(code));
            return info;

        }

        /// <summary>
        /// 保存储值卡信息
        /// </summary>
        /// <param name="entity"></param>
        /// <param name="cardAdmTxns"></param>
        /// <returns></returns>
        public async Task saveCard(cardEntity entity, cardAdmTxnsEntity cardAdmTxns)
        {
            //插入储值卡信息
            await UnitOfWork.Set<cardEntity>().AddAsync(entity);
            //插订单支付记录
            await UnitOfWork.Set<cardAdmTxnsEntity>().AddAsync(cardAdmTxns);
            //工作单元
            await UnitOfWork.CommitAsync();

        }


        /// <summary>
        /// 保存交易记录
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public async Task savePayTradeLog(cardEntity entity, cardTxnsEntity cardTxns)
        {
            //更新储值卡信息
            UnitOfWork.Set<cardEntity>().Update(entity);
            //插订单支付记录
            await UnitOfWork.Set<cardTxnsEntity>().AddAsync(cardTxns);
            //工作单元
            await UnitOfWork.CommitAsync();
        }

    }
}
