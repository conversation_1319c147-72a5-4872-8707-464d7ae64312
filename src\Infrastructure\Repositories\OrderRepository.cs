﻿using BPM.Datas.EntityFramework.Core;
using BPM.Uow;
using Domain.Models.order;
using Domain.Models.pay;
using Domain.Models.sync;
using Domain.Models.user;
using Domain.order.Repository;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Infrastructure.Repositories;

/// <summary>
/// 版 本 BPM敏捷开发框架
/// 创建人：Aarons
/// 日 期：2022-07-09
/// 描 述：订单仓储
/// </summary>
public class OrderRepository : RepositoryBase<shopTxnsEntity, string>, IOrderRepository
{
    /// <summary>
    /// 初始化
    /// </summary>
    /// <param name="unitOfWork"></param>
    public OrderRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
    {

    }

    /// <summary>
    /// 获取销售记录信息
    /// </summary>
    /// <param name="txnnum"></param>
    /// <returns></returns>
    public async Task<List<shopTxnsEntity>> getShopTxnsList(string txnnum)
    {
        //return await this.FindAllAsync(x => x.TXNNUM == txnnum);
        var list = await this.FindAllAsync(x => x.TXNNUM == txnnum);
        return list.OrderBy(x => x.SEQUENCE).ToList();

    }
    /// <summary>
    /// 保存交易记录
    /// </summary>
    /// <param name="entity"></param>
    /// <returns></returns>
    public async Task savePayTradeLog(payTradeLogEntity entity)
    {
        //插订单支付记录
        await UnitOfWork.Set<payTradeLogEntity>().AddAsync(entity);
        //工作单元
        await UnitOfWork.CommitAsync();
    }

    /// <summary>
    /// 保存用户操作日志
    /// </summary>
    /// <param name="entity"></param>
    /// <returns></returns>
    public async Task saveUserLogs(List<userLogsEntity> list)
    {
        //插记录
        await UnitOfWork.Set<userLogsEntity>().AddRangeAsync(list);
    }


    /// <summary>
    /// 获取订单日志记录
    /// </summary>
    /// <param name="shop_id"></param>
    /// <param name="order_no"></param>
    /// <returns></returns>
    public syncLogsEntity getSyncLogs(string shop_id, string order_no)
    {
        //插订单支付记录
        return UnitOfWork.Set<syncLogsEntity>().Where(x => x.ShopId == shop_id && x.SourceId == order_no).FirstOrDefault();

    }


    /// <summary>
    ///  插入订单同步日志
    /// </summary>
    /// <param name="entity"></param>
    /// <returns></returns>
    public async Task saveSyncLogs(syncLogsEntity entity)
    {
        entity.Id = Guid.NewGuid().ToString();
        entity.SyncDate = DateTime.Now;
        //插订单支付记录
        await UnitOfWork.Set<syncLogsEntity>().AddAsync(entity);
        //工作单元
        await UnitOfWork.CommitAsync();
    }


    /// <summary>
    /// 插入订单明细
    /// </summary>
    /// <param name="list"></param>
    /// <returns></returns>
    public async Task savePurgingOrder(List<shopTxnsEntity> list)
    {
        await UnitOfWork.Set<shopTxnsEntity>().AddRangeAsync(list);

    }

}
