﻿using Microsoft.EntityFrameworkCore;
using System;

namespace Infrastructure.UnitOfWorks.SqlServer
{
    /// <summary>
    /// 工作单元
    /// </summary>
    public class BaseUnitOfWork : BPM.Datas.EntityFramework.SqlServer.UnitOfWork, IBaseUnitOfWork
    {
        /// <summary>
        /// 初始化一个<see cref="BaseUnitOfWork"/>类型的实例
        /// </summary>
        /// <param name="options">配置项</param>
        public BaseUnitOfWork(DbContextOptions<BaseUnitOfWork> options, IServiceProvider serviceProvider) : base(options, serviceProvider)
        {

        }
    
    }
}
