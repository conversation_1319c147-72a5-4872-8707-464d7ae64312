﻿using Microsoft.EntityFrameworkCore;
using System;

namespace Infrastructure.UnitOfWorks.SqlServer
{
    /// <summary>
    /// 储值卡工作单元
    /// </summary>
    public class CardUnitOfWork : BPM.Datas.EntityFramework.SqlServer.UnitOfWork, ICardUnitOfWork
    {
        /// <summary>
        /// 初始化一个<see cref="CardUnitOfWork"/>类型的实例
        /// </summary>
        /// <param name="options">配置项</param>
        public CardUnitOfWork(DbContextOptions<CardUnitOfWork> options, IServiceProvider serviceProvider) : base(options, serviceProvider)
        {

        }
    
    }
}
