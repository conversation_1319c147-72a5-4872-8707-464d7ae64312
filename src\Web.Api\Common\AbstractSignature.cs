﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Collections.Generic;

namespace Web.Api.Common;

public abstract class AbstractSignature
{
    abstract public string Sign(Dictionary<string, object> data, string signKey);

    public  static string SignOriginalString(Dictionary<string, object> data, string signKey)
    {
        return DictionarySortJoinUtil.SortJoin(data, "&", "=", true, signKey);
    }

    public static dynamic GetSortObject(object obj)
    {
        if (obj is JArray)
        {
            var list = new List<dynamic>();
            foreach (var item in (obj as JArray))
            {
                list.Add(GetSortObject(item));
            }
            return list;
        } else if (obj is JValue)
        {
            return obj;
        }
        else
        {
            var paramDic = JsonConvert.DeserializeObject<Dictionary<string, dynamic>>(JsonConvert.SerializeObject(obj));
            var sortedDic = new Dictionary<string, dynamic>();
            for (int i = 0; i < paramDic.Count; i++)
            {
                if (paramDic.ElementAt(i).Value is JArray || paramDic.ElementAt(i).Value is JObject)
                {
                    sortedDic.Add(paramDic.ElementAt(i).Key, GetSortObject(paramDic.ElementAt(i).Value));
                }
                else
                {
                    sortedDic.Add(paramDic.ElementAt(i).Key, paramDic.ElementAt(i).Value);
                }
            }
            return DictionarySortJoinUtil.AsciiDictionary(sortedDic);
        }
    }
}
public class DictionarySortJoinUtil
{
    public static string SortJoin(Dictionary<string, object> param, string separator, string keyValueSeparator, bool ignoreNull,
                              params string[] otherParams)
    {
        Dictionary<string, object> sortedDict = AsciiDictionary(param);
        return Join(sortedDict, separator, keyValueSeparator, ignoreNull, otherParams);
    }

    public static string Join(Dictionary<string, object> dict, string separator, string keyValueSeparator, bool ignoreNull, params string[] otherParams)
    {
        StringBuilder strBuilder = new StringBuilder();
        bool first = true;
        if (!IsEmpty(dict))
        {
            foreach (KeyValuePair<string, object> kvp in dict)
            {
                if (!ignoreNull || (kvp.Key != null && kvp.Value != null))
                {
                    if (first)
                    {
                        first = false;
                    }
                    else
                    {
                        strBuilder.Append(separator);
                    }
                    object value = kvp.Value;
                    string valueStr = "";
                    if (value is string)
                    {
                        valueStr = Convert.ToString(value);
                    }
                    else
                    {
                        valueStr = JsonConvert.SerializeObject(value);
                    }
                    strBuilder.Append(kvp.Key).Append(keyValueSeparator).Append(valueStr);
                }
            }
        }
        // 补充其它字符串到末尾，默认无分隔符
        if (IsNotEmpty(otherParams))
        {
            foreach (string otherParam in otherParams)
            {
                strBuilder.Append(otherParam);
            }
        }
        return strBuilder.ToString();
    }

    public static bool IsNotEmpty(params string[] array)
    {
        return (array != null && array.Length != 0);
    }

    public static bool IsEmpty(Dictionary<string, object> dict)
    {
        return null == dict || dict.Count == 0;
    }

    public static Dictionary<string, object> AsciiDictionary(Dictionary<string, object> sArray)
    {
        Dictionary<string, object> asciiDic = new Dictionary<string, object>();
        string[] arrKeys = sArray.Keys.ToArray();
        Array.Sort(arrKeys, string.CompareOrdinal);
        foreach (var key in arrKeys)
        {
            object value = sArray[key];
            if (value is Dictionary<string, object>)
            {
                value = AsciiDictionary((Dictionary<string, object>)value);
            }
            asciiDic.Add(key, value);
        }
        return asciiDic;
    }
}
