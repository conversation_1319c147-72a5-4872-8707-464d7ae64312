﻿using System.Collections.Generic;

namespace Web.Api.Common;

public class AppSignHelper
{

    private static string FormatQueryParamMap(Dictionary<string, object> paraMap)
    {
        try
        {
            string buff = "";
            var result = from pair in paraMap orderby pair.Key select pair;
            foreach (KeyValuePair<string, object> pair in result)
            {
                if (!pair.Key.IsEmpty() && !pair.Value.IsNull() && pair.Value != "")
                {
                    string key = pair.Key;
                    string val = pair.Value.ToString();
                    buff += key + "=" + val + "&";
                }
            }

            if (buff.Length == 0 == false)
            {
                buff = buff.Substring(0, (buff.Length - 1) - (0));
            }
            return buff;
        }
        catch (Exception e)
        {
            Console.Error.WriteLine(e.StackTrace);
            return "";
        }
    }

    public static string FormatQueryJsonMap(string json)
    {
        string buff = "";
        try
        {
            Dictionary<string, string> paraMap = json.ToObject<Dictionary<string, string>>();
            var result = from pair in paraMap orderby pair.Key select pair;
            foreach (KeyValuePair<string, string> pair in result)
            {
                if (pair.Key != "" && pair.Value != "" && pair.Key != "sign")
                {
                    string key = pair.Key;
                    string val = pair.Value;
                    buff += key + "=" + val + "&";
                }
            }

            if (buff.Length == 0 == false)
            {
                buff = buff.Substring(0, buff.Length - 1 - 0);
            }
        }
        catch (Exception e)
        {

        }
        return buff;
    }
    /// <summary>
    /// 参数签名
    /// </summary>
    /// <param name="bizObj">参与签名的参数</param>
    /// <param name="appkey">应用密钥</param>
    /// <returns></returns>
    public static string QueryParamSign(Dictionary<string, object> Params, string appkey)
    {
        string unSignParamString = FormatQueryParamMap(Params);
        return MD5Sign(unSignParamString, appkey);
    }

    /// <summary>
    /// Md5 签名
    /// </summary>
    /// <param name="content"></param>
    /// <param name="key"></param>
    /// <returns></returns>
    public static string MD5Sign(string content, string key)
    {
        String signStr = "";
        if (key == "") return null;
        if (content == "") return null;
        signStr = content + key;
        return Md5Hash(signStr);
    }


    /// <summary>
    /// 32位MD5加密（小写）
    /// </summary>
    /// <param name="input">输入字段</param>
    /// <returns></returns>
    private static string Md5Hash(string input)
    {
        var md5Hasher = new System.Security.Cryptography.MD5CryptoServiceProvider();
        byte[] data = md5Hasher.ComputeHash(Encoding.Default.GetBytes(input));
        StringBuilder sBuilder = new StringBuilder();
        for (int i = 0; i < data.Length; i++)
        {
            sBuilder.Append(data[i].ToString("x2"));
        }
        return sBuilder.ToString();
    }

}
