﻿using System.Collections.Generic;
using System.Reflection;

namespace Web.Api.Common;

public class DictionaryHelper
{
    /// <summary>
    /// 实体转键值对
    /// </summary>
    /// <typeparam name="T">泛型</typeparam>
    /// <param name="obj"></param>
    /// <returns></returns>
    public static Dictionary<string, object> EntityToDictionary<T>(T obj) where T : class
    {
        //初始化定义一个键值对，注意最后的括号
        var dic = new Dictionary<string, object>();
        //返回当前 Type 的所有公共属性Property集合
        PropertyInfo[] props = typeof(T).GetProperties();
        foreach (PropertyInfo p in props)
        {
            var property = obj.GetType().GetProperty(p.Name);//获取property对象
            var value = p.GetValue(obj);//获取属性值
            dic.Add(p.Name, valueOf(value));
        }
        return dic;
    }

    private static string valueOf(Object obj)
    {
        return (obj == null) ? null : obj.ToString().Trim();
    }


}

