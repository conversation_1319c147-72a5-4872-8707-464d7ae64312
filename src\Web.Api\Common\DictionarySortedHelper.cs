﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;
using System.Collections.Generic;
using System.Reflection;

namespace Web.Api.Common
{
    /// <summary>
    ///字典排序
    /// </summary>
    public class DictionarySortedHelper
    {
        /// <summary>
        /// 获取排序对象
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public static dynamic GetSortObject(object obj)
        {
            if (obj is JArray)
            {
                var list = new List<dynamic>();
                foreach (var item in (obj as JArray))
                    list.Add(GetSortObject(item));
                return list;
            }
            else if (obj is JValue) return obj;
            else
            {
                var paramDic = JsonConvert.DeserializeObject<Dictionary<string, dynamic>>(JsonConvert.SerializeObject(obj));
                var sortedDic = new Dictionary<string, dynamic>();
                for (int i = 0; i < paramDic.Count; i++)
                {
                    if (paramDic.ElementAt(i).Value is JArray || paramDic.ElementAt(i).Value is JObject)
                        sortedDic.Add(paramDic.ElementAt(i).Key, GetSortObject(paramDic.ElementAt(i).Value));
                    else
                        sortedDic.Add(paramDic.ElementAt(i).Key, paramDic.ElementAt(i).Value);
                }
                return AsciiDictionary(sortedDic);
            }
        }

        /// <summary>
        /// 字典排序
        /// </summary>
        /// <param name="sArray"></param>
        /// <returns></returns>
        public static Dictionary<string, object> AsciiDictionary(Dictionary<string, object> sArray)
        {
            Dictionary<string, object> dictionary = new Dictionary<string, object>();
            string[] array = sArray.Keys.ToArray();
            Array.Sort(array, string.CompareOrdinal);
            string[] array2 = array;
            foreach (string key in array2)
            {
                object value = sArray[key];
                dictionary.Add(key, value);
            }
            return dictionary;
        }

        /// <summary>
        /// 排序拼接
        /// </summary>
        /// <param name="param"></param>
        /// <param name="separator"></param>
        /// <param name="keyValueSeparator"></param>
        /// <param name="ignoreNull"></param>
        /// <returns></returns>
        public static string SortJoin(Dictionary<string, object> param, string separator, string keyValueSeparator, bool ignoreNull)
        {
            Dictionary<string, object> dict = AsciiDictionary(param);
            return Join(dict, separator, keyValueSeparator, ignoreNull);
        }
        /// <summary>
        /// 拼接
        /// </summary>
        /// <param name="dict">字典</param>
        /// <param name="separator">分隔符</param>
        /// <param name="keyValueSeparator"></param>
        /// <param name="ignoreNull">是否排除空</param>
        /// <returns></returns>
        public static string Join(Dictionary<string, object> dict, string separator, string keyValueSeparator, bool ignoreNull)
        {
            StringBuilder stringBuilder = new StringBuilder();
            bool flag = true;
            if (!dict.IsNullOrEmpty())
            {
                foreach (KeyValuePair<string, object> item in dict)
                {
                    if (item.Key != null && item.Value != null && item.Value != "")
                    {
                        if (flag)
                        {
                            flag = false;
                        }
                        else
                        {
                            stringBuilder.Append(separator);
                        }
                        object value = item.Value;
                        string text = "";
                        text = ((!(value is string)) ? value.ToJson(true) : Convert.ToString(value));
                        stringBuilder.Append(item.Key).Append(keyValueSeparator).Append(text);
                    }
                }
            }
            return stringBuilder.ToString();
        }
    }
}
