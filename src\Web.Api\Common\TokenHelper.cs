﻿using Domain.Shared;
using System.Collections.Generic;
using Web.Api.Configs;

namespace Web.Api.Common
{

    /// <summary>
    /// Token服务
    /// </summary>
    public class TokenHelper : BPMControllerBase
    {
        /// <summary>
        /// 缓存服务接口
        /// </summary>
        private readonly ICache _cache;
        /// <summary>
        /// 接口服务配置
        /// </summary>
        private readonly IServiceConfigProvider _serviceConfigProvider;

        public TokenHelper(ICache cache, IServiceConfigProvider serviceConfigProvider)
        {
            _cache = cache;
            _serviceConfigProvider = serviceConfigProvider;
        }
        // 够造函数必须是私有的，不能被外部直接调用。
        private TokenHelper()
        {
        }

        private static TokenHelper _instance;
        // 暴露给外部，提供实例。
        public static TokenHelper getInstance(ICache cache, IServiceConfigProvider serviceConfigProvider)
        {
            if (_instance == null)
            {
                _instance = new TokenHelper(cache, serviceConfigProvider);
            }
            return _instance;
        }


        /// <summary>
        /// 获取Token
        /// </summary>
        /// <returns></returns>
        public async Task<ApiResult> getAccessToken()
        {
            var config = await _serviceConfigProvider.GetConfig();
            var res = new ApiResult(BPM.AspNetCore.Mvc.StatusCode.Fail);
            //查询缓存是否存在token
            string token = await _cache.GetAsync<string>(CacheKey.cacheToken + "_" + config.app_id);
            //缓存中存在token
            if (!token.IsEmpty())
            {
                res.Data = token;
                res.Code = (int)BPM.AspNetCore.Mvc.StatusCode.Ok;
                return res;
            }
            else
            {
                var post_url = config.service_url + "/api/v1/oauth/token";
                var postData = $"grant_type=client_credentials&client_id={config.app_id}&client_secret={config.app_secret}";
                string req = await BPM.Helpers.Web.Client().Post(post_url + "?" + postData).ContentType(BPM.Http.Clients.HttpContentType.FormUrlEncoded).ResultAsync();
                var obj = req.ToJObject();
                if (obj["code"].ToString() == "200")
                {
                    obj = obj["data"].ToString().ToJObject();
                    int expires_in = 0;//缓存时间(分钟)
                    if (!int.TryParse(obj["expiresIn"].ToString(), out expires_in))
                        expires_in = 7200;
                    expires_in = expires_in - 120;
                    //Token存储缓存
                    await _cache.AddAsync<string>(CacheKey.cacheToken + "_" + config.app_id, obj["accessToken"].ToString(), TimeSpan.FromSeconds(expires_in));
                    res.Code = (int)BPM.AspNetCore.Mvc.StatusCode.Ok;
                    res.Data = obj["accessToken"].ToString();
                    return res;
                }
                else
                    return new ApiResult((int)BPM.AspNetCore.Mvc.StatusCode.Fail, obj["info"].ToString());
            }
        }


        public async Task<ApiResult> getYzAccessToken(string grant_id = "")
        {
            var config = await _serviceConfigProvider.GetConfig();
            if (grant_id != "")
                config.grant_id = grant_id;
            var res = new ApiResult(BPM.AspNetCore.Mvc.StatusCode.Fail);
            //查询缓存是否存在token
            string token = await _cache.GetAsync<string>(CacheKey.cacheToken + config.grant_id);
            //缓存中存在token
            if (!token.IsEmpty())
            {
                res.Data = token;
                res.Code = (int)BPM.AspNetCore.Mvc.StatusCode.Ok;
                return res;
            }
            else
            {
                var param = new Dictionary<string, object>
                {
                    { "client_id", config.app_id },
                    { "client_secret", config.app_secret },
                    { "authorize_type", config.AuthorizeType },
                    { "grant_id", config.grant_id },
                    { "refresh", config.Refresh }
                };

                var post_url = config.service_url + "/auth/token";
                string req = await BPM.Helpers.Web.Client().Post(post_url).ContentType(BPM.Http.Clients.HttpContentType.Json).Data(param).ResultAsync();
                var obj = req.ToJObject();
                if (obj["code"].ToString() == "200")
                {
                    obj = obj["data"].ToString().ToJObject();
                    long expires_in = long.Parse(obj["expires"].ToString());//缓存时间(分钟)
                    expires_in = expires_in / 1000;
                    //Token存储缓存
                    await _cache.AddAsync<string>(CacheKey.cacheToken + config.grant_id, obj["access_token"].ToString()
                        , TimeSpan.FromMilliseconds(expires_in));
                    res.Code = (int)BPM.AspNetCore.Mvc.StatusCode.Ok;
                    res.Data = obj["access_token"].ToString();
                    return res;
                }
                else
                    return new ApiResult((int)BPM.AspNetCore.Mvc.StatusCode.Fail, obj["message"].ToString());
            }
        }

    }
}
