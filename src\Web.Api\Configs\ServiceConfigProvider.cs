﻿namespace Web.Api.Configs
{
    /// <summary>
    /// 配置提供器
    /// </summary>
    public class ServiceConfigProvider : IServiceConfigProvider
    {
        /// <summary>
        /// 配置
        /// </summary>
        private readonly ServiceOptions _config;

        /// <summary>
        /// 初始化一个<see cref="ServiceConfigProvider"/>类型的实例
        /// </summary>
        /// <param name="config">支付宝配置</param>
        public ServiceConfigProvider(ServiceOptions config)
        {
            _config = config;
        }

        /// <summary>
        /// 获取配置
        /// </summary>
        /// <returns></returns>
        public Task<ServiceOptions> GetConfig()
        {
            return Task.FromResult(_config);
        }


        /// <summary>
        /// 设置支付
        /// </summary>
        /// <param name="config">配置对象</param>
        public void SetConfig(ServiceOptions config) 
        {
            _config.app_id = config.app_id;
            _config.app_secret = config.app_secret;
            _config.grant_id = config.grant_id;
        }


    }
}
