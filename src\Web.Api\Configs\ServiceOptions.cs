﻿namespace Web.Api.Configs
{
    /// <summary>
    /// 接口服务配置
    /// </summary>
    public class ServiceOptions
    {

        /// <summary>
        /// 服务地址
        /// </summary>
        public string service_url { get; set; }

        /// <summary>
        /// 应用编号
        /// </summary>
        public string app_id { get; set; }

        /// <summary>
        /// 应用密钥
        /// </summary>
        public string app_secret { get; set; }

        /// <summary>
        /// 店铺id
        /// </summary>
        public string grant_id { get; set; }

        /// <summary>
        /// 授权方式
        /// </summary>
        public string AuthorizeType { get; set; } = "silent";

        /// <summary>
        /// 是否刷新
        /// </summary>
        public bool Refresh { get; set; } = false;
    }
}
