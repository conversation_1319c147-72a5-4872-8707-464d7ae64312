﻿using Api.Filter;
using BPM.Caching;
using Web.Api.Common;
using Web.Api.Configs;

namespace Web.Api.Controllers
{

    /// <summary>
    /// 用户登录授权控制器
    /// </summary>
    [Route("api/auth")]
    [AllowAnonymous]
    public class AuthorizationController : ApiControllerBase
    {
        /// <summary>
        /// 应用授权
        /// </summary>
        private readonly IOAuthAppService _securityAppService;

        /// <summary>
        /// 授权服务
        /// </summary>
        private readonly IApplicationService _appService;

        /// <summary>
        /// 门店应用服务
        /// </summary>
        private readonly IShopAppService _shopAppService;

        /// <summary>
        /// 接口服务配置
        /// </summary>
        private readonly IServiceConfigProvider _serviceConfigProvider;

        /// <summary>
        /// 缓存服务接口
        /// </summary>
        private readonly ICache _cache;


        /// <summary>
        /// 初始化服务
        /// </summary>
        /// <param name="securityAppService">应用服务</param>
        public AuthorizationController(IOAuthAppService securityAppService, IApplicationService appService
            , IShopAppService shopAppService, IServiceConfigProvider serviceConfigProvider, ICache cache)
        {
            _securityAppService = securityAppService;
            _appService = appService;
            _shopAppService = shopAppService;
            _serviceConfigProvider = serviceConfigProvider;
            _cache = cache;

        }

        /// <summary>
        /// 获取令牌
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns></returns>
        //[HttpPost, Route("access_token")]
        [NoSignAttribute, HttpPost, Route("access_token")]
        public async Task<IActionResult> access_token([FromBody] accessTokenRequest request)
        {
            var appAccount = await _appService.GetAppAccountInfo(request.app_id);
            if (appAccount.IsNull())
                return Fail("终端应用不存在");
            var terminal = await _shopAppService.getTerminalInfoByMac(request.mac);
            if (terminal.IsNull())
                return Fail($"Mac未在服务器注册:[{request.mac}]");
            var app = new AppInfo();
            app.AppId = request.app_id;
            app.AppName = appAccount.app_type;
            app.UserId = request.app_id;
            var result = await _securityAppService.SignInAsync(app);
            var tokeninfo = result.Token;
            var jsonResult = new
            {
                access_token = tokeninfo.AccessToken,
                expires_date = "",
                expires_in = tokeninfo.AccessTokenUtcExpires,
                token_type = "Bearer",
                ref_token = tokeninfo.RefreshToken
            };
            return Success(jsonResult);
        }

        /// <summary>
        /// 刷新令牌
        /// </summary>
        /// <param name="token">刷新令牌</param>
        [HttpPost, Route("refresh_token")]
        public async Task<IActionResult> RefreshTokenAsync([FromBody] refreshTokenRequest request)
        {
            var result = await _securityAppService.RefreshTokenAsync(request.ref_token);
            var jsonResult = new
            {
                access_token = result.AccessToken,
                expires_date = "",
                expires_in = result.AccessTokenUtcExpires,
                token_type = "Bearer",
                ref_token = result.RefreshToken
            };
            return Success(jsonResult);
        }

        /// <summary>
        /// 获取有赞令牌
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns></returns>
        //[HttpPost, Route("access_token")]
        [NoSignAttribute, HttpPost, Route("yz_access_token")]
        public async Task<IActionResult> yz_access_token([FromBody] accessTokenRequest request)
        {
            var res = await TokenHelper.getInstance(_cache, _serviceConfigProvider).getYzAccessToken();
            return Success(res);


        }
    }
}