﻿using Api.Filter;
using AppService.Dtos.coupon;
using AutoMapper;
using BPM.Data.Sql;
using BPM.Extras.Youzan.Request;
using BPM.Extras.Youzan.Responses;
using BPM.Extras.Youzan.Services;
using BPM.Logs.Contents;
using BPM.Mapper;
using Domain.Shared;
using System.Collections;
using System.Collections.Generic;
using static BPM.Extras.Youzan.Request.orderCouponVerifyCalculateRequest;


/// <summary>
///  会员控制器
/// </summary>
[Route("api/coupon")]
[Authorize]
public class couponController : ApiControllerBase
{
    /// <summary>
    /// 门店应用服务
    /// </summary>
    private readonly IShopAppService _shopAppService;

    /// <summary>
    /// 商品应用服务
    /// </summary>
    private readonly IProdcutAppService _prodcutAppService;


    /// <summary>
    /// sql 执行器
    /// </summary>
    protected ISqlExecutor _sqlExecutor { get; set; }

    /// <summary>
    /// 有赞服务接口
    /// </summary>
    private readonly IYouzanService _youzanService;

    /// <summary>
    /// 初始化服务
    /// </summary>
    /// <param name="prodcutAppService">应用服务</param>
    public couponController(ISqlExecutor sqlExecutor, IShopAppService shopAppService, IProdcutAppService prodcutAppService, IYouzanService youzanService)
    {
        _sqlExecutor = sqlExecutor;
        _shopAppService = shopAppService;
        _prodcutAppService = prodcutAppService;
        _youzanService = youzanService;
    }

    /// <summary>
    /// 优惠券列表
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [HttpPost, Route("order-coupon")]
    public async Task<IActionResult> getOrderCouponList([FromBody] couponOrderListRequest request)
    {
        var reader = await _sqlExecutor.ExecuteReaderAsync($"EXEC Get_Coupon @TXNNUM='{request.orderNo}'");
        var data = DataConvertHelper.ToDataTable(reader);
        return Success(data);
    }

    /// <summary>
    /// 优惠券列表/获取下单前用户优惠凭证列表
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [HttpPost, Route("write-off/list")]
    public async Task<IActionResult> getYzCouponList([FromBody] couponListRequest request)
    {
        var shop_info = await _shopAppService.getShopInfoById(request.shopId);
        if (shop_info == null)
            return Fail("门店信息不存在");
        var request_yz = new orderCouponRequest();
        request_yz.buyer_identity.yz_open_id = request.openId;
        var orderItems = request.items.ToObject<List<orderItemList>>();
        foreach (var item in orderItems)
        {
            var product_info = await _prodcutAppService.getProductSkuInfo(item.outerProductCode);

            request_yz.order_items.Add(new orderCouponRequest.orderItems()
            {
                item_id = product_info == null ? item.outerProductCode : product_info.product_id,
                sku_id = product_info == null ? item.outerProductCode : product_info.id,
                order_item_promotional_price = Convert.ToInt64(Convert.ToDecimal(item.promoPrice) * 100),
                num = item.saleQty.ToInt(),
                order_item_id = item.orderItemNo.ToLong(),
                root_item_id = product_info == null ? item.outerProductCode : product_info.product_id
            });
        }
        var parameter = new YouzanParameter();
        parameter.url = $"/api/youzan.ump.voucher.list.beforeorder/1.0.0";
        var dict = new Dictionary<string, object>();
        dict.Add("request", request_yz);
        parameter.body = dict;
        parameter.grant_id = shop_info.source_no;
        var response = await _youzanService.getYouzanData(parameter);
        WriteLog("获取下单前用户优惠凭证列表", parameter.url, dict.ToJson(), response.ToJson());
        if (response.success)
        {
            var result = response.data.ToString().ToObject<List<orderCouponResponse>>();
            // 不可用优惠券列表
            var notAvailableCouponList = new List<couponQueryListDto>();
            // 可用优惠券列表
            var availableCouponList = new List<couponQueryListDto>();

            var available_list = result.Where(x => x.available == true && x.preferential_mode == 1);
            foreach (var item in available_list)
            {
                availableCouponList.Add(new couponQueryListDto()
                {
                    couponId = item.voucher_identity.coupon_id,
                    remark = item.threshold_copywriter,
                    useThreshold = (item.threshold * 0.01).ToString(),
                    actualDiscount = item.max_discount.ToString(),
                    couponNo = item.verify_code,
                    couponType = item.voucher_identity.coupon_type,
                    description = item.value_desc,
                    discount = (item.voucher_value / 100.00).ToString(),
                    name = item.title,
                    useStartTime = DateTimeExtensions.ConvertLongToDateTime(item.valid_start_time).ToString(),
                    useEndTime = DateTimeExtensions.ConvertLongToDateTime(item.valid_end_time).ToString(),
                    can_overlay_with_other = item.can_overlay_with_other,
                    can_overlay_with_self = item.can_overlay_with_self,
                    activity_id = item.activity_id,
                });
            }
            var notAvailable_list = result.Where(x => x.available == false || x.preferential_mode != 1);
            foreach (var item in notAvailable_list)
            {
                notAvailableCouponList.Add(new couponQueryListDto()
                {
                    couponId = item.voucher_identity.coupon_id,
                    //useThreshold = item.threshold_copywriter,
                    useThreshold = (item.threshold * 0.01).ToString(),
                    actualDiscount = item.max_discount.ToString(),
                    couponNo = item.verify_code,
                    couponType = item.voucher_identity.coupon_type,
                    description = item.value_desc,
                    discount = (item.voucher_value / 100.00).ToString(),
                    name = item.title,
                    useStartTime = DateTimeExtensions.ConvertLongToDateTime(item.valid_start_time).ToString(),
                    useEndTime = DateTimeExtensions.ConvertLongToDateTime(item.valid_end_time).ToString(),
                    remark = string.IsNullOrEmpty(item.reason) && item.preferential_mode != 1 ? "线下门店不适用" : item.reason,
                    can_overlay_with_other = item.can_overlay_with_other,
                    can_overlay_with_self = item.can_overlay_with_self,
                    activity_id = item.activity_id,
                });
            }
            var josnData = new { notAvailableCouponList, availableCouponList };
            return Success(josnData);
        }
        return Fail(response.message);
    }

    /// <summary>
    /// 叠加优惠凭证计算优惠
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [HttpPost, Route("write-off/verify"), AllowAnonymous, NoSign]
    public async Task<IActionResult> getYzCouponVerify([FromBody] couponCheckVerifyRequest request)
    {
        var shop_info = await _shopAppService.getShopInfoById(request.shopId);
        if (shop_info == null)
            return Fail("门店信息不存在");
        var couponIds = request.couponNoList.ToObject<ArrayList>();
        var request_yz = new orderCouponVerifyCalculateRequest();
        request_yz.yz_open_id = request.openId;
        request_yz.kdt_id = shop_info.source_no;
        // 订单明细
        var orderItems = request.items.ToObject<List<orderItemList>>();
        foreach (var item in orderItems)
        {
            var product_info = await _prodcutAppService.getProductSkuInfo(item.outerProductCode);
            request_yz.order_items.Add(new orderCouponVerifyCalculateRequest.orderItems()
            {
                item_id = product_info == null ? item.outerProductCode : product_info.product_id,
                sku_id = product_info == null ? item.outerProductCode : product_info.id,
                goods_promotional_price = Convert.ToInt64(Convert.ToDecimal(item.promoPrice) * 100),
                goods_original_price = Convert.ToInt64(Convert.ToDecimal(item.salePrice) * 100),
                num = item.saleQty.ToInt(),
                oid = item.orderItemNo.ToLong(),
                root_item_id = product_info == null ? item.outerProductCode : product_info.product_id
            });
        }
        var preferential_list = new List<voucherPreferentialItemList>();
        // 循环优惠券id
        foreach (var coupon_id in couponIds)
        {
            preferential_list.Add(new voucherPreferentialItemList()
            {
                // 叠加订单明细
                voucher_order_items = request_yz.order_items.MapToList<voucherPreferentialItemList.voucherOrderItems>(),
                // 优惠券汇总
                preferential_value = request_yz.order_items.Sum(s => s.goods_promotional_price),
                // 叠加优惠券凭证
                voucher_identity = new voucherPreferentialItemList.voucherIdentity()
                {
                    coupon_id = coupon_id.ToString(),
                    coupon_type = 0
                }
            });
        }
        // 叠加明细
        request_yz.voucher_preferential_item_list = preferential_list;
        var parameter = new YouzanParameter();
        parameter.url = $"/api/youzan.ump.voucher.verify.calculate/2.0.0";
        parameter.body = request_yz;
        parameter.grant_id = shop_info.source_no;
        var response = await _youzanService.getYouzanData(parameter);
        WriteLog("叠加优惠凭证计算优惠", parameter.url, request_yz.ToJson(), response.ToJson());
        if (response.success)
        {
            var result = response.data.ToString().ToObject<List<orderCouponCheckVerifyResponse>>();
            // 不可用优惠券列表
            var notAvailableCouponList = new List<couponQueryListDto>();
            // 可用优惠券列表
            var availableCouponList = new List<couponQueryListDto>();

            var available_list = result.Where(x => x.available == true);
            foreach (var item in available_list)
            {
                availableCouponList.Add(new couponQueryListDto()
                {
                    couponId = item.voucher_identity.coupon_id,
                    useThreshold = item.threshold_copywriter,
                    actualDiscount = item.maxDiscount.ToString(),
                    // couponNo = item.verify_code,
                    couponType = item.voucher_identity.coupon_type,
                    description = item.value_desc,
                    discount = (item.voucher_value / 100.00).ToString(),
                    name = item.title,
                    useStartTime = DateTimeExtensions.ConvertLongToDateTime(item.valid_start_time).ToString(),
                    useEndTime = DateTimeExtensions.ConvertLongToDateTime(item.valid_end_time).ToString(),
                });
            }
            var notAvailable_list = result.Where(x => x.available == false);
            foreach (var item in notAvailable_list)
            {
                notAvailableCouponList.Add(new couponQueryListDto()
                {
                    couponId = item.voucher_identity.coupon_id,
                    useThreshold = item.threshold_copywriter,
                    actualDiscount = item.maxDiscount.ToString(),
                    //couponNo = item.verify_code,
                    couponType = item.voucher_identity.coupon_type,
                    description = item.value_desc,
                    discount = (item.voucher_value / 100.00).ToString(),
                    name = item.title,
                    useStartTime = DateTimeExtensions.ConvertLongToDateTime(item.valid_start_time).ToString(),
                    useEndTime = DateTimeExtensions.ConvertLongToDateTime(item.valid_end_time).ToString(),
                    remark = item.reason
                });
            }
            var josnData = new { notAvailableCouponList, availableCouponList };
            return Success(josnData);
        }
        return Fail(response.message);
    }


    /// <summary>
    /// 核销优惠券
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [HttpPost, Route("write-off")]
    public async Task<IActionResult> writeOffCoupon([FromBody] couponVerifyRequest request)
    {
        var shop_info = await _shopAppService.getShopInfoById(request.shopId);
        if (shop_info == null)
            return Fail("门店信息不存在");
        var parameter = new YouzanParameter();
        parameter.url = $"/api/youzan.ump.voucher.back.verify/1.0.0";
        var coupon_no_list = request.couponNoList.ToObject<List<string>>();
        var coupon_no = coupon_no_list[0];
        var request_yz = new orderCouponVerifyRequest()
        {
            verify_code = coupon_no,
            verify_in_order = request.orderNo,
            verified_kdt_id = shop_info.source_no,
            kdt_id = shop_info.parent_id,
            preferential_value = request.amount
        };
        var dict = new Dictionary<string, object>();
        dict.Add("open_voucher_dto", request_yz);
        dict.Add("is_push_msg", false);
        parameter.body = dict;
        parameter.grant_id = shop_info.source_no;
        var response = await _youzanService.getYouzanData(parameter);
        WriteLog("用户优惠券核销", parameter.url, dict.ToJson(), response.ToJson());
        if (response.success)
        {
            var result = response.data.ToString().ToObject<orderCouponVerifyResponse>();
            if (result.status == 1)
                return Success(result);
            return Fail("优惠券已核销或不可用");
        }
        return Fail(response.message);
    }

    /// <summary>
    /// 撤消优惠券
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [HttpPost, Route("write-off/cancel")]
    public async Task<IActionResult> cancelCancel([FromBody] couponCancelRequest request)
    {
        var shop_info = await _shopAppService.getShopInfoById(request.shopId);
        if (shop_info == null)
            return Fail("门店信息不存在");
        var parameter = new YouzanParameter();
        parameter.url = $"/api/youzan.ump.back.verify.refund/1.0.0";
        var request_yz = new orderCouponRevertRequest()
        {
            coupon_id = request.couponId,
            //0-优惠券；1-优惠码
            coupon_type = 0
        };
        var dict = new Dictionary<string, object>
            {
                { "is_push_msg", true }, // 是否发核销完成消息
                { "open_voucher_dto", new
                    {
                        kdt_id = shop_info.parent_id, // 必须是总店店铺id
                        verify_in_order = request.orderNo, // 核销的订单号
                        voucher_identity = request_yz // 添加 coupon_identity
                    }
                }
            };
        parameter.body = dict;
        parameter.grant_id = shop_info.source_no;
        var response = await _youzanService.getYouzanData(parameter);
        WriteLog("用户优惠券撤销", parameter.url, dict.ToJson(), response.ToJson());
        if (response.success)
        {
            var result = response.data.ToString().ToObject<orderCouponVerifyResponse>();
            if (response.code == "200")
                return Success(result);
            return Fail("优惠券撤销失败");
        }
        return Fail(response.message);

    }



    /// <summary>
    /// 获取买家优惠券详情
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [HttpPost, Route("queryCoupon")]
    public async Task<IActionResult> queryCoupon([FromBody] couponQueryRequest request)
    {
        var shop_info = await _shopAppService.getShopInfoById(request.shopId);
        if (shop_info == null)
            return Fail("门店信息不存在");
        var parameter = new YouzanParameter();
        parameter.url = $"/api/youzan.ump.voucher.query.detail/1.0.0";
        var dict = new Dictionary<string, object>();
        dict.Add("coupon_id", request.couponId);
        dict.Add("coupon_type", 0);
        parameter.body = dict;
        parameter.grant_id = shop_info.source_no;
        var response = await _youzanService.getYouzanData(parameter);
        WriteLog("获取买家优惠券详情", parameter.url, dict.ToJson(), response.ToJson());
        if (response.success)
        {
            var result = response.data.ToString().ToObject<orderCouponVerifyResponse>();
            if (result.status == 3)
                return Success(result);
            return Fail("优惠券状态不是【已核销】");
        }
        return Fail(response.message);
    }

    /// <summary>
    /// 日志记录
    /// </summary>
    /// <param name="title">标题</param>
    /// <param name="pay_way">支付网关</param>
    /// <param name="requestData">请求数据</param>
    /// <param name="rawData">原始响应</param>
    private void WriteLog(string title, string pay_way, string requestData, string resultData)
    {
        var content = new StringBuilder();
        content.AppendLine($"请求地址:{pay_way}");
        content.AppendLine($"请求参数:{requestData}");
        content.AppendLine($"返回结果:{resultData}");
        Log.Set<LogContent>(p => p.Class = GetType().FullName)
            .Set<LogContent>(p => p.Caption = title)
            .Set<LogContent>(p => p.Content = content)
           .Info();
    }

}

/// <summary>
///  优惠券关系映射
/// </summary>
public class mapperProfile : Profile, IObjectMapperProfile
{
    public mapperProfile()
    {
        CreateMap<orderCouponVerifyCalculateRequest.orderItems, voucherPreferentialItemList.voucherOrderItems>()
           .ForMember(d => d.goods_preferential_value, opt => opt.MapFrom(s => s.goods_promotional_price));
    }

    public void CreateMap()
    {

    }
}