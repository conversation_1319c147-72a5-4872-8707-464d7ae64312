﻿using BPM.Helpers;
using System.Collections.Generic;
using Web.Api.Common;
using Web.Api.Configs;

namespace Web.Api.Controllers
{

    /// <summary>
    ///  客户控制器
    /// </summary>
    [Route("api/scrm/customer")]
    [Authorize]
    public class customerController : ApiControllerBase
    {
        /// <summary>
        /// 客户应用服务
        /// </summary>
        private readonly IMemberAppService _memberAppService;
        /// <summary>
        /// 接口服务配置
        /// </summary>
        private readonly IServiceConfigProvider _serviceConfigProvider;

        /// <summary>
        /// 缓存服务接口
        /// </summary>
        private readonly ICache _cache;

        /// <summary>
        /// 初始化服务
        /// </summary>
        /// <param name="prodcutAppService">应用服务</param>
        public customerController(IMemberAppService memberAppService, IServiceConfigProvider serviceConfigProvider, ICache cache)
        {
            _memberAppService = memberAppService;
            _serviceConfigProvider = serviceConfigProvider;
            _cache = cache;
        }

        /// <summary>
        /// 获取会员卡信息
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns></returns>
        [HttpPost, Route("get_customer_info")]
        public async Task<IActionResult> getCustomerInfo(memberRequest request)
        {
            var res = await TokenHelper.getInstance(_cache, _serviceConfigProvider).getAccessToken();
            if (res.Code == (int)BPM.AspNetCore.Mvc.StatusCode.Ok)
            {
                var token = res.Data;
                var configs = await _serviceConfigProvider.GetConfig();
                var timestamp = DataTimeHelper.DateTimeToLongTimeStamp(System.DateTime.Now);
                var param = new Dictionary<string, object>();
                param.Add("accountId", request.code);
                param.Add("accountType", 1);// 账号类型: 1 - 手机号码，2 - 微信unionId，3 - 微信openId，4 - 灵智平台的唯一会员id(推荐使用)，5 - 外部原会员系统会员卡号
                param.Add("timestamp", timestamp.ToString());
                var sign = Common.AppSignHelper.QueryParamSign(param, configs.grant_id);
                var requstData = param.ToJson(true);
                var encryptData = Encrypt.RsaEncryptJava(requstData, configs.grant_id, Encoding.UTF8);
                var post_url = configs.service_url + $"/ds/v1/customer/get?access_token={token}&timestamp={timestamp}&sign={sign}";
                var PostData = new { key = encryptData };
                string req = await BPM.Helpers.Web.Client().Post(post_url).JsonData(PostData).ResultAsync();
                var obj = req.ToJObject();
                if (obj["code"].ToString() == "200")
                {
                    var resultData = obj["data"].ToString().ToJObject();
                    //客户编号
                    var customer_id = resultData["customerId"].ToString();
                    string pointReq = await getCustomerPointInfo(token, customer_id, configs);
                    var obj_point = pointReq.ToJObject();
                    if (obj_point["code"].ToString() == "200")
                    {
                        var resultPointData = obj_point["data"].ToString().ToJObject();
                    }
                }
                //var jsonData = new
                //{
                //    data = data, //数据
                //    total_page = 1,//总页数
                //    page = 1,//当前页
                //    records = data.Count,//总记录数
                //};
                return Success(req);
            }
            return Fail(res.info);
        }

        /// <summary>
        /// 获取会员积分信息
        /// </summary>
        /// <param name="token"></param>
        /// <param name="accountId"></param>
        /// <param name="configs"></param>
        /// <returns></returns>
        private async Task<string> getCustomerPointInfo(string token, string accountId, ServiceOptions configs)
        {
            var timestamp = DataTimeHelper.DateTimeToLongTimeStamp(System.DateTime.Now);
            var param = new Dictionary<string, object>();
            param.Add("accountId", accountId);
            param.Add("accountType", 4);// 账号类型: 1 - 手机号码，2 - 微信unionId，3 - 微信openId，4 - 灵智平台的唯一会员id(推荐使用)，5 - 外部原会员系统会员卡号
            param.Add("timestamp", timestamp.ToString());
            var sign = Common.AppSignHelper.QueryParamSign(param, configs.grant_id);
            var requstData = param.ToJson(true);
            var encryptData = Encrypt.RsaEncryptJava(requstData, configs.grant_id, Encoding.UTF8);
            var post_url = configs.service_url + $"/ds/v1/customer/point/get?access_token={token}&timestamp={timestamp}&sign={sign}";
            var PostData = new { key = encryptData };
            string req = await BPM.Helpers.Web.Client().Post(post_url).JsonData(PostData).ResultAsync();
            return req;

        }

    }
}