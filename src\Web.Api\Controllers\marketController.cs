﻿namespace Web.Api.Controllers
{

    /// <summary>
    ///  促销控制器
    /// </summary>
    [Route("api/market")]
    [Authorize]
    public class marketController : ApiControllerBase
    {
        /// <summary>
        /// 促销应用服务
        /// </summary>
        private readonly IMarketAppService  _marketAppService;

        /// <summary>
        /// 初始化服务
        /// </summary>
        /// <param name="prodcutAppService">应用服务</param>
        public marketController(IMarketAppService marketAppService)
        {
            _marketAppService = marketAppService;
        }

        /// <summary>
        /// 获取促销活动列表
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns></returns>
        [HttpPost, Route("get_promotions_list")]
        public async Task<IActionResult> getPagePromotionsList([FromBody] promotionsRequest request)
        {
            var query = new promotionsQuery();
            query.Page = request.page_index;
            query.PageSize = request.page_size;
            query.terminal_id = request.terminal_id;
            query.Order = "UID DESC";
            var result = await _marketAppService.getPagePromotionsList(query);
            var jsonData = new
            {
                data = result.Data, //数据
                total_page = result.PageCount,//总页数
                page = result.Page,//当前页
                records = result.TotalCount,//总记录数
            };
            return Success(jsonData);
        }

        /// <summary>
        /// 获取买赠活动列表
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns></returns>
        [HttpPost, Route("get_gifthdr_list")]
        public async Task<IActionResult> getPageGifthdrList([FromBody] gifthdrRequest request)
        {
            var query = new promotionsQuery();
            query.Page = request.page_index;
            query.PageSize = request.page_size;
            query.shop_id = request.shop_id;
            query.Order = "UID DESC";
            var result = await _marketAppService.getPageGifthdrList(query);
            var jsonData = new
            {
                data = result.Data, //数据
                total_page = result.PageCount,//总页数
                page = result.Page,//当前页
                records = result.TotalCount,//总记录数
            };
            return Success(jsonData);
        }


        /// <summary>
        /// 获取买赠活动明细列表
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns></returns>
        [HttpPost, Route("get_giftdtl_list")]
        public async Task<IActionResult> getPageGiftdtlList([FromBody] gifthdrRequest request)
        {
            var query = new promotionsQuery();
            query.Page = request.page_index;
            query.PageSize = request.page_size;
            query.shop_id = request.shop_id;
            query.Order = "GIFTNUM DESC";
            var result = await _marketAppService.getPageGiftdtlList(query);
            var jsonData = new
            {
                data = result.Data, //数据
                total_page = result.PageCount,//总页数
                page = result.Page,//当前页
                records = result.TotalCount,//总记录数
            };
            return Success(jsonData);
        }

        /// <summary>
        /// 获取换购活动列表
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns></returns>
        [HttpPost, Route("get_trade_items_list")]
        public async Task<IActionResult> getPageTradeItemsList([FromBody] tradeItemsRequest request)
        {
            var query = new promotionsQuery();
            query.Page = request.page_index;
            query.PageSize = request.page_size;
            query.shop_id = request.shop_id;
            query.Order = "TRADENUM DESC";
            var result = await _marketAppService.getPageTradeItemsList(query);
            var jsonData = new
            {
                data = result.Data, //数据
                total_page = result.PageCount,//总页数
                page = result.Page,//当前页
                records = result.TotalCount,//总记录数
            };
            return Success(jsonData);
        }

    }
}