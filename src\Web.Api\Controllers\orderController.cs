﻿using Api.Filter;
using Azure.Core;
using BPM.Data.Sql;
using BPM.Events.Messages;
using DotNetCore.CAP;
using System.Data;
using System.Diagnostics;
using Web.Api.Configs;
using Web.Api.EventHandlers.Youzan;
using static Web.Api.EventHandlers.Youzan.orderRefundMessageEvent;
using static Web.Api.EventHandlers.Youzan.orderTradeMessageEvent;

namespace Web.Api.Controllers
{

    /// <summary>
    ///  订单控制器
    /// </summary>
    [Route("api/order")]
    [Authorize]
    public class orderController : ApiControllerBase
    {
        /// <summary>
        /// 订单应用服务
        /// </summary>
        private readonly IOrderAppService _orderAppService;

        /// <summary>
        /// 消息事件总线
        /// </summary>
        private readonly IMessageEventBus _eventBus;

        /// <summary>
        /// 消息事件总线
        /// </summary>
        private readonly ICapPublisher _capPublisher;

        /// <summary>
        /// 门店应用服务接口
        /// </summary>
        private readonly IShopAppService _shopAppService;

        /// <summary>
        /// 应用配置
        /// </summary>
        private readonly AppOptions _appOptions;
        /// <summary>
        /// sql 执行器
        /// </summary>
        protected ISqlExecutor _sqlExecutor { get; set; }

        /// <summary>
        /// 初始化服务
        /// </summary>
        /// <param name="orderAppService">应用服务</param>
        public orderController(IOrderAppService orderAppService, IMessageEventBus eventBus, IConfiguration configuration, ISqlExecutor sqlExecutor
            , IShopAppService shopAppService, ICapPublisher capPublisher)
        {
            _orderAppService = orderAppService;
            _eventBus = eventBus;
            _appOptions = configuration.GetSection("AppOptions").Get<AppOptions>();
            _sqlExecutor = sqlExecutor;
            _shopAppService = shopAppService;
            _capPublisher = capPublisher;
        }


        /// <summary>
        /// 补充未同步的订单
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [AllowAnonymous, NoSign, HttpPost, Route("sync_lz_order")]
        public async Task<IActionResult> saveLzOrder(string txn_time = "")
        {
            if (txn_time.IsEmpty())
                txn_time = DateTime.Now.AddDays(-5).ToString("yyyy-MM-dd");
            var shopList = await _shopAppService.getTerminalList();
            var data = await _orderAppService.NotSyncTxnumDt("", txn_time);
            if (!data.IsNull() && data.Rows.Count > 0 && _appOptions.enableRainbow)
            {
                foreach (DataRow dr in data.Rows)
                {
                    if (dr["TXNTYPE"].ToString() == "1")
                    {

                        var refund = new refundMessage();
                        refund.order_sn = dr["TXNNUM"].ToString();
                        // 基础方式
                        await _capPublisher.PublishAsync("orderRefundEvent", refund);
                        // 扩展方式
                        //await _eventBus.PublishAsync(new orderRefundMessageEvent(refund));
                    }
                    else
                    {

                        var trade = new tradeMessage();
                        trade.order_sn = dr["TXNNUM"].ToString();
                        // 基础方式
                        await _capPublisher.PublishAsync("orderEvent", trade);
                        // 扩展方式
                        // await _eventBus.PublishAsync(new orderTradeMessageEvent(trade));
                    }
                    await Task.Delay(500); // 等待1000毫秒（1秒）
                    Log.Info($"补充未同步的订单-->{dr["TXNNUM"].ToString()}");
                }
            }
            return Success("同步成功");
        }

        /// <summary>
        /// 补充历史订单同步(不计算积分)
        /// </summary>
        /// <param name="txn_time">交易日期</param>
        /// <returns></returns>
        [AllowAnonymous, NoSign, HttpPost, Route("sync_history_order")]
        public async Task<IActionResult> syncHistoryOrder(string txn_time = "")
        {
            if (txn_time.IsEmpty())
                txn_time = DateTime.Now.AddDays(-5).ToString("yyyy-MM-dd");
            //var shopList = await _shopAppService.getTerminalList();
            var data = await _orderAppService.NotSyncTxnumDt("", txn_time);
            if (!data.IsNull() && data.Rows.Count > 0 && _appOptions.enableRainbow)
            {
                foreach (DataRow dr in data.Rows)
                {
                    if (dr["TXNTYPE"].ToString() == "1")
                    {
                        var refund = new refundMessage();
                        refund.order_sn = dr["TXNNUM"].ToString();
                        //await _eventBus.PublishAsync(new orderRefundHistoryMessageEvent(refund));
                        await _capPublisher.PublishAsync("orderRefundHistoryEvent", refund);
                    }
                    else
                    {
                        var trade = new tradeMessage();
                        trade.order_sn = dr["TXNNUM"].ToString();
                        await _capPublisher.PublishAsync("orderHistoryEvent", trade);
                       // await _eventBus.PublishAsync(new orderTradeHistoryMessageEvent(trade));
                    }

                    Log.Info($"补充历史订单同步-->{dr["TXNNUM"].ToString()}");
                }
            }
            return Success("同步成功");
        }

        /// <summary>
        /// 订单同步
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [AllowAnonymous, NoSign, HttpPost, Route("sync_order")]
        public async Task<IActionResult> saveOrder([FromBody] orderRequest request)
        {
            try
            {
                Log.Info($"订单同步开始，订单号:{request.TXNNUM}，会员标识:{request.is_member}，销售类型:{request.is_sale}，Rainbow启用状态:{_appOptions.enableRainbow}");

                var number = await _orderAppService.saveOrder(request);
                var jsonData = new { number = number };

                Log.Info($"订单同步，订单保存完成-->{request.TXNNUM}，返回number:{number}");
                Log.Info($"订单同步，接到订单同步请求-->{request.TXNNUM}");

                if (request.is_member && _appOptions.enableRainbow)
                {
                    Log.Info($"订单同步，满足发布条件，准备发布事件-->{request.TXNNUM}");

                    try
                    {
                        if (request.is_sale)
                        {
                            var trade = new tradeMessage();
                            trade.order_sn = request.TXNNUM;
                            Log.Info($"订单同步，发布订单事件前-->{request.TXNNUM}");
                            //await _eventBus.PublishAsync(new orderTradeMessageEvent(trade));
                            await _capPublisher.PublishAsync("orderEvent", trade);
                            Log.Info($"订单同步，发布订单事件后-->{request.TXNNUM}");
                        }
                        else
                        {
                            var refund = new refundMessage();
                            refund.order_sn = request.TXNNUM;
                            Log.Info($"订单同步，发布退单事件前-->{request.TXNNUM}");
                            //await _eventBus.PublishAsync(new orderRefundMessageEvent(refund));
                            await _capPublisher.PublishAsync("orderRefundEvent", refund);
                            Log.Info($"订单同步，发布退单事件后-->{request.TXNNUM}");
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.Error($"订单同步，发布事件异常-->{request.TXNNUM}，异常信息:{ex.Message}，堆栈:{ex.StackTrace}");
                        // 继续执行，不影响主流程
                    }
                }
                else
                {
                    Log.Info($"订单同步，不满足发布条件，不发布事件-->{request.TXNNUM}，is_member:{request.is_member}，enableRainbow:{_appOptions.enableRainbow}");
                }

                return Success(jsonData);
            }
            catch (Exception ex)
            {
                Log.Error($"订单同步整体异常-->{request.TXNNUM}，异常信息:{ex.Message}，堆栈:{ex.StackTrace}");
                throw; // 重新抛出异常，让全局异常处理器处理
            }
        }

        /// <summary>
        /// 清机订单同步
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [AllowAnonymous, NoSign, HttpPost, Route("sync_purg_order")]
        public async Task<IActionResult> savePurgingOrder([FromBody] orderPurgRequest request)
        {
            var number = await _orderAppService.savePurgingOrder(request);
            var jsonData = new { number = number };
            return Success(jsonData);
        }
    }
}