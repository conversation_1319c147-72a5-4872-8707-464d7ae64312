﻿using Api.Filter;
using AppService.Dtos.pay;
using BPM.Logs.Contents;
using Domain.Shared.Constant;
using Essensoft.Paylink.Alipay;
using Essensoft.Paylink.Alipay.Domain;
using Essensoft.Paylink.Alipay.Request;
using Essensoft.Paylink.Alipay.Response;
using Essensoft.Paylink.WeChatPay;
using Essensoft.Paylink.WeChatPay.V2;
using Essensoft.Paylink.WeChatPay.V2.Request;
using Essensoft.Paylink.WeChatPay.V2.Response;

namespace Web.Api.Controllers
{

    /// <summary>
    ///  支付控制器
    /// </summary>
    [Route("api/pay")]
    [Authorize]
    public class payController : ApiControllerBase
    {
        /// <summary>
        /// 支付应用服务
        /// </summary>
        private readonly IPayAppService _payAppService;
        /// <summary>
        /// 微信支付客户端
        /// </summary>
        private readonly IWeChatPayClient _weChatPayClient;
        /// <summary>
        /// 支付宝客户端
        /// </summary>
        private readonly IAlipayClient _alipayClient;
        /// <summary>
        /// 设备信息
        /// </summary>
        private static string device_info = "";

        /// <summary>
        /// 初始化服务
        /// </summary>
        /// <param name="orderAppService">订单应用服务</param>
        /// <param name="weChatPayClient">微信支付提供器</param>
        /// <param name="alipayClient">支付宝提供器</param>
        public payController(IPayAppService payAppService, IWeChatPayClient weChatPayClient, IAlipayClient alipayClient)
        {
            _payAppService = payAppService;
            _weChatPayClient = weChatPayClient;
            _alipayClient = alipayClient;
        }

        /// <summary>
        /// 获取支付列表
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns></returns>
        [HttpPost, Route("get_paymethod_list")]
        public async Task<IActionResult> getPaymethodList([FromBody] baseRequest request)
        {
            var query = new baseQuery();
            query.Page = request.page_index;
            query.PageSize = request.page_size;
            query.Order = "CODE DESC";
            var result = await _payAppService.getPagePaymethodList(query);
            var jsonData = new
            {
                data = result.Data, //数据
                total_page = result.PageCount,//总页数
                page = result.Page,//当前页
                records = result.TotalCount,//总记录数
            };
            return Success(jsonData);
        }

        /// <summary>
        /// 网络支付
        /// </summary>
        /// <returns></returns>
        [HttpPost, Route("bar_code_pay")]
        public async Task<IActionResult> barCodePay(netPaymentRequset request)
        {
            device_info = request.shop_id + "-" + request.device_id;
            Log.Info("网络支付请求参数----->" + request.ToJson());
            var res = new ApiResult(BPM.AspNetCore.Mvc.StatusCode.Fail);
            //当前条形码前缀
            string prx = request.auth_code.Substring(0, 2);
            //微信前缀
            string[] wechat_prx = _configuration.GetSection("PaymentPrx")["wechat_prx"].Split(',');
            //支付宝前缀
            string[] alipay_prx = _configuration.GetSection("PaymentPrx")["alipay_prx"].Split(',');
            var trade_fee = request.trade_fee.ToDecimal();//待支付金额
            if (trade_fee > 0)
            {
                //微信支付
                if (wechat_prx.Contains(prx))
                    res = await weChatPayQrCode(request);
                //支付宝支付
                if (alipay_prx.Contains(prx))
                    res = await aliPayQrCode(request);
                if (res.Code == (int)BPM.AspNetCore.Mvc.StatusCode.Ok)
                    return Success(res);
                return Fail(res.info);
            }
            else return Fail("支付金额不合法");
        }

        /// <summary>
        /// 网络退款
        /// </summary>
        /// <returns></returns>
        [HttpPost, Route("bar_code_refund")]
        public async Task<IActionResult> barCodeRefund(netRefundRequset request)
        {
            Log.Info("网络退款请求参数----->" + request.ToJson());
            device_info = request.shop_id + "-" + request.device_id;
            var res = new ApiResult(BPM.AspNetCore.Mvc.StatusCode.Fail);
            //当前条形码前缀
            string prx = request.payment_sn.Substring(0, 2);
            //微信前缀
            string[] wechat_prx = _configuration.GetSection("PaymentPrx")["wechat_prx"].Split(',');
            //支付宝前缀
            string[] alipay_prx = _configuration.GetSection("PaymentPrx")["alipay_prx"].Split(',');
            var paylogs = await _payAppService.getPayTradeLog(new payLogsQuery() { pay_sn = request.payment_sn });
            var pay = paylogs.Where(p => p.trade_type == "1").FirstOrDefault();
            if (pay.IsNull())
                return Fail("交易记录不存在");
            var refunds = paylogs.Where(p => p.trade_type == "2").ToList();
            //已退款金额
            decimal? refund_total_fee = 0.00M;
            if (refunds.Count > 0)
                refund_total_fee = refunds.Sum(p => p.trade_fee);
            if (refund_total_fee + request.refund_fee.ToDecimal() > pay.trade_fee)
                return Fail("退款总金额不能大于交易金额");
            //微信支付
            if (wechat_prx.Contains(prx))
                res = await wechatRefund(request.shop_id, request.order_sn, request.payment_sn, request.refund_sn, (decimal)pay.trade_fee, request.refund_fee.ToDecimal());
            //支付宝支付
            if (alipay_prx.Contains(prx))
                res = await alipayRefund(request.shop_id, request.order_sn, request.payment_sn, request.refund_sn, request.refund_fee.ToDecimal());
            if (res.Code == (int)BPM.AspNetCore.Mvc.StatusCode.Ok)
            {
                return Success("支付成功");
            }
            return Fail(res.info);


        }

        /// <summary>
        /// 网络订单查询
        /// </summary>
        /// <param name="requset"></param>
        /// <returns></returns>
        [HttpPost, Route("bar_code_query")]
        public async Task<IActionResult> barCodeQuery(netQueryRequset request)
        {
            //当前条形码前缀
            string prx = request.payment_sn.Substring(0, 2);
            //微信前缀
            string[] wechat_prx = _configuration.GetSection("PaymentPrx")["wechat_prx"].Split(',');
            //支付宝前缀
            string[] alipay_prx = _configuration.GetSection("PaymentPrx")["alipay_prx"].Split(',');
            //微信支付
            if (wechat_prx.Contains(prx))
            {
                var wechat_info = await _payAppService.getShopWechatPay(request.shop_id);
                if (wechat_info.IsNull())
                {
                    return Fail("微信配置不存在");
                }
                var config = new WeChatPayOptions();
                config.AppId = wechat_info.app_id;
                config.MchId = wechat_info.mch_id;
                config.SubAppId = wechat_info.sub_appid;
                config.SubMchId = wechat_info.sub_mch_id;
                config.APIKey = wechat_info.pay_key;
                config.Certificate = BPM.Helpers.Web.RootPath + $"/cert/{wechat_info.cert_path}";

                Log.Info($"微信支付证书路径----->{config.Certificate}");
                var result = await wachatOrderQuery(request.payment_sn, config);

                switch (result.TradeState)
                {
                    case "SUCCESS": // 支付成功
                        return Success(new
                        {
                            trade_state = result.TradeState,
                            trade_fee = result.TotalFee * 0.01,
                            trade_time = result.TimeEnd,
                            trade_desc = ""
                        });

                    case "NOTPAY": // 未支付
                        return Success(new
                        {
                            trade_state = "WAIT_PAY",  // 统一返回格式
                            trade_fee = result.TotalFee * 0.01,
                            trade_time = result.TimeEnd,
                            trade_desc = "等待买家付款"
                        });

                    case "CLOSED": // 已关闭
                        return Success(new
                        {
                            trade_state = "CLOSED",
                            trade_fee = 0,
                            trade_time = result.TimeEnd,
                            trade_desc = "交易关闭"
                        });

                    case "REVOKED": // 已撤销（刷卡支付）
                        return Success(new
                        {
                            trade_state = "CLOSED",
                            trade_fee = 0,
                            trade_time = result.TimeEnd,
                            trade_desc = "交易已撤销"
                        });

                    case "USERPAYING": // 用户支付中
                        return Success(new
                        {
                            trade_state = "WAIT_PAY",
                            trade_fee = result.TotalFee * 0.01,
                            trade_time = result.TimeEnd,
                            trade_desc = "用户支付中"
                        });

                    case "PAYERROR": // 支付失败
                        return Success(new
                        {
                            trade_state = "FAIL",
                            trade_fee = 0,
                            trade_time = result.TimeEnd,
                            trade_desc = "支付失败" + (result.ErrCodeDes ?? "")
                        });

                    default:
                        return Success(new
                        {
                            trade_state = "UNKNOWN",
                            trade_fee = 0,
                            trade_time = result.TimeEnd,
                            trade_desc = $"未知交易状态:{result.TradeState}"
                        });
                }
            }
            //支付宝支付
            if (alipay_prx.Contains(prx))
            {
                var alipay_info = await _payAppService.getShopAliPay(request.shop_id);
                if (alipay_info.IsNull())
                    return new ApiResult(BPM.AspNetCore.Mvc.StatusCode.Fail, "支付宝配置不存在！");
                var config = new AlipayOptions();
                config.AppId = alipay_info.alipay_app_id;
                config.AppPrivateKey = alipay_info.merchant_private_key;
                config.AlipayPublicKey = alipay_info.alipay_public_key;
                config.SignType = alipay_info.sign_type;
                var result = await aliPayQuery(config, request.payment_sn);

                // 先检查API调用是否成功
                if (result.Code == "10000")
                {
                    // 根据交易状态返回对应结果
                    switch (result.TradeStatus)
                    {
                        case "TRADE_SUCCESS":  // 交易支付成功
                        case "TRADE_FINISHED": // 交易结束，不可退款
                            return Success(new
                            {
                                trade_state = "SUCCESS",
                                trade_fee = result.TotalAmount,
                                trade_time = result.SendPayDate,
                                trade_desc = ""
                            });

                        case "WAIT_BUYER_PAY": // 交易创建，等待买家付款
                            return Success(new
                            {
                                trade_state = "WAIT_PAY",
                                trade_fee = result.TotalAmount,
                                trade_time = result.SendPayDate,
                                trade_desc = "等待买家付款"
                            });

                        case "TRADE_CLOSED": // 未付款交易超时关闭，或支付完成后全额退款
                            return Success(new
                            {
                                trade_state = "CLOSED",
                                trade_fee = 0,
                                trade_time = result.SendPayDate,
                                trade_desc = "交易关闭"
                            });

                        default:
                            return Success(new
                            {
                                trade_state = "UNKNOWN",
                                trade_fee = 0,
                                trade_time = result.SendPayDate,
                                trade_desc = $"未知交易状态:{result.TradeStatus}"
                            });
                    }
                }

                return Success(new
                {
                    trade_state = "FAIL",
                    trade_fee = 0,
                    trade_time = result.SendPayDate,
                    trade_desc = result.SubMsg
                });
            }
            return Fail("交易规则不合法");
        }


        #region 私有方法    

        #region 微信支付

        /// <summary>
        /// 微信支付
        /// </summary>
        /// <returns></returns>
        private async Task<ApiResult> weChatPayQrCode(netPaymentRequset requset)
        {
            Log.Info("微信支付请求参数----->" + requset.ToJson());
            var res = new ApiResult(BPM.AspNetCore.Mvc.StatusCode.Fail);
            var wechat_info = await _payAppService.getShopWechatPay(requset.shop_id);
            if (wechat_info.IsNull())
            {
                Log.Info("微信配置不存在----->" + requset.shop_id);
                res.info = "微信配置不存在";
                return res;
            }
            var config = new WeChatPayOptions();
            config.AppId = wechat_info.app_id;
            config.MchId = wechat_info.mch_id;
            config.SubAppId = wechat_info.sub_appid;
            config.SubMchId = wechat_info.sub_mch_id;
            config.APIKey = wechat_info.pay_key;
            config.Certificate = BPM.Helpers.Web.RootPath + $"/cert/{wechat_info.cert_path}";
             
            //支付之前先查一次该交易是否已经支付
            var response = await wachatOrderQuery(requset.payment_sn, config);
            Log.Info("微信支付前查询结果----->" + response.ToJson());
            
            //表示已经支付过了,防止客户端和接口掉包的情况
            if (response.ReturnCode == "SUCCESS" && response.ResultCode == "SUCCESS" && response.TradeState == "SUCCESS")
            {
                Log.Info("微信订单已支付----->" + response.ToJson());
                res.Code = (int)BPM.AspNetCore.Mvc.StatusCode.Ok;
                res.info = "订单已支付";
                res.Data = new { TradeState = response.TradeState };
                return res;
            }
            //表示客户已撤销 
            if (response.TradeState == "REVOKED")
            {
                Log.Info("微信订单已撤销----->" + response.ToJson());
                res.Code = (int)BPM.AspNetCore.Mvc.StatusCode.Fail;
                res.info = response.TradeStateDesc + "请重新支付！";
                res.Data = new { TradeState = response.TradeState };
                return res;
            }

            //设置应用参数
            var payRequset = new WeChatPayMicroPayRequest
            {
                Body = requset.attach,
                DeviceInfo = requset.shop_id + "-" + requset.device_id,
                OutTradeNo = requset.payment_sn,
                TotalFee = (requset.trade_fee.ToDecimal() * 100).ToString().ToInt(),
                AuthCode = requset.auth_code,
                SceneInfo = new { id = requset.shop_id, name = requset.shop_name }.ToJson()
            };
            Log.Info("微信支付请求参数----->" + payRequset.ToJson());
            var result = await _weChatPayClient.ExecuteAsync(payRequset, config);
            Log.Info("微信支付返回结果----->" + result.ToJson());
            
            //记录文本日志
            WriteLog("微信支付", payRequset.GetRequestUrl(), payRequset.ToJson(), result.ToJson(), result.Body);
            
            var request = new payTradeLogDto();
            request.order_id = requset.order_sn;
            request.trade_type = tardeConstant.pay;
            request.trade_fee = Convert.ToDecimal((payRequset.TotalFee / 100.00));
            request.pay_code = paymentConstant.wechatPay;
            request.pay_sn = payRequset.OutTradeNo;
            request.trade_no = result.TransactionId;
            request.json = result.Body;
            request.qr_code = requset.auth_code;
            request.shop_id = requset.shop_id;

            // 处理支付结果
            if (result.ReturnCode == "FAIL")
            {
                Log.Info("微信支付请求失败----->" + result.ToJson());
                res.info = result.ReturnMsg;
                res.Data = new { TradeState = "FAIL" };
                return res;
            }

            if (result.ResultCode == "FAIL")
            {
                //等待用户授权
                if (result.ErrCode == "USERPAYING")
                {
                    Log.Info("微信等待用户支付，开始轮询----->" + result.ToJson());
                    //查询订单轮询
                    var pollResult = await wachatPollQuery(config, requset.payment_sn);
                    //30 秒还未支付
                    if (pollResult.TradeState == "USERPAYING")
                    {
                        //撤销订单
                        //await wachatOrderReverse(payment_sn, config);
                        Log.Info("微信等待时间超过30秒----->" + pollResult.ToJson());
                        res.info = "等待时间超过30秒,请重新扫描支付！";
                        res.Data = new { TradeState = pollResult.TradeState };
                        return res;
                    }
                    //客户支付失败 || 取消订单 
                    if (pollResult.TradeState == "PAYERROR" || pollResult.TradeState == "TRADE_ERROR")
                    {
                        //撤销订单
                        //await wachatOrderReverse(payment_sn, config);
                        Log.Info("微信支付失败或取消----->" + pollResult.ToJson());
                        res.info = "支付失败或已取消,请重新支付!";
                        res.Data = new { TradeState = pollResult.TradeState };
                        return res;
                    }
                    if (pollResult.ReturnCode == "SUCCESS" && pollResult.ResultCode == "SUCCESS" && pollResult.TradeState == "SUCCESS")
                    {
                        Log.Info("微信支付成功(轮询)----->" + pollResult.ToJson());
                        request.json = pollResult.Body;
                        await savePayTradeLog(request);
                        res.Code = (int)BPM.AspNetCore.Mvc.StatusCode.Ok;
                        res.info = "支付成功";
                        res.Data = new { TradeState = pollResult.TradeState };
                        return res;
                    }
                    else
                    {
                        Log.Info("微信支付失败----->" + pollResult.ToJson());
                        res.info = pollResult.ErrCodeDes ?? pollResult.ReturnMsg;
                        res.Data = new { TradeState = pollResult.TradeState };
                        return res;
                    }
                }
                else
                {
                    Log.Info("微信支付业务失败----->" + result.ToJson());
                    res.info = result.ErrCodeDes;
                    res.Data = new { TradeState = "FAIL" };
                    return res;
                }
            }

            if (result.ReturnCode == "SUCCESS" && result.ResultCode == "SUCCESS" && result.TradeType == "MICROPAY")
            {
                Log.Info("微信支付成功----->" + result.ToJson());
                await savePayTradeLog(request);
                res.Code = (int)BPM.AspNetCore.Mvc.StatusCode.Ok;
                res.info = "支付成功";
                res.Data = new { TradeState = "SUCCESS" };
                return res;
            }

            Log.Info("微信支付异常----->" + result.ToJson());
            res.info = "支付异常，请重试";
            res.Data = new { TradeState = "FAIL" };
            return res;
        }

        /// <summary>
        /// 微信退款
        /// </summary>
        /// <param name="store_id">门店编号</param>
        /// <param name="order_sn">订单编号</param>
        /// <param name="payment_sn">交易单号</param>
        /// <param name="refund_sn">退款单号</param>
        /// <param name="trade_fee">交易金额(元)</param>
        /// <param name="refund_fee">退款金额(元)</param>
        /// <returns></returns>
        private async Task<ApiResult> wechatRefund(string store_id, string order_sn, string payment_sn, string refund_sn, decimal trade_fee, decimal refund_fee)
        {
            Log.Info($"微信退款请求参数----->店铺ID:{store_id},订单号:{order_sn},交易号:{payment_sn},退款单号:{refund_sn},交易金额:{trade_fee},退款金额:{refund_fee}");
            var res = new ApiResult(BPM.AspNetCore.Mvc.StatusCode.Fail);
            var wechat_info = await _payAppService.getShopWechatPay(store_id);
            if (wechat_info.IsNull())
            {
                Log.Info("微信配置不存在----->" + store_id);
                res.info = "微信配置不存在";
                return res;
            }

            // 查询原交易状态
            var config = new WeChatPayOptions();
            config.AppId = wechat_info.app_id;
            config.MchId = wechat_info.mch_id;
            config.SubAppId = wechat_info.sub_appid;
            config.SubMchId = wechat_info.sub_mch_id;
            config.APIKey = wechat_info.pay_key;
            config.Certificate = BPM.Helpers.Web.RootPath + $"/cert/{wechat_info.cert_path}";

            var queryResponse = await wachatOrderQuery(payment_sn, config);
            Log.Info("微信退款前查询结果----->" + queryResponse.ToJson());

            if (queryResponse.ReturnCode != "SUCCESS" || queryResponse.ResultCode != "SUCCESS")
            {
                Log.Info("微信原交易查询失败----->" + queryResponse.ToJson());
                res.info = "原交易查询失败：" + (queryResponse.ReturnMsg ?? queryResponse.ErrCodeDes);
                return res;
            }

            // if (queryResponse.TradeState != "SUCCESS")
            // {
            //     Log.Info("微信交易状态不正确----->" + queryResponse.ToJson());
            //     res.info = "交易状态不正确，当前状态：" + queryResponse.TradeState;
            //     return res;
            // }

            var payRequset = new WeChatPayRefundRequest
            {
                OutRefundNo = refund_sn,
                OutTradeNo = payment_sn,
                TotalFee = (trade_fee * 100).ToString().ToInt(),
                RefundFee = (refund_fee * 100).ToString().ToInt()
            };
            Log.Info("微信退款请求参数----->" + payRequset.ToJson());
            var result = await _weChatPayClient.ExecuteAsync(payRequset, config);
            Log.Info("微信退款返回结果----->" + result.ToJson());

            //记录文本日志
            WriteLog("微信退款", payRequset.GetRequestUrl(), payRequset.ToJson(), result.ToJson(), result.Body);

            if (result.ReturnCode == "FAIL")
            {
                Log.Info("微信退款请求失败----->" + result.ToJson());
                res.info = "退款请求失败：" + result.ReturnMsg;
                return res;
            }

            if (result.ResultCode == "FAIL")
            {
                Log.Info("微信退款业务失败----->" + result.ToJson());
                res.info = "退款失败：" + result.ErrCodeDes;
                return res;
            }

            if (result.ResultCode == "SUCCESS")
            {
                Log.Info("微信退款成功----->" + result.ToJson());
                res.Code = (int)BPM.AspNetCore.Mvc.StatusCode.Ok;
                var request = new payTradeLogDto();
                request.order_id = order_sn;
                request.trade_type = tardeConstant.refund;
                request.trade_fee = refund_fee;
                request.pay_code = paymentConstant.wechatPay;
                request.pay_sn = payment_sn;
                request.trade_no = result.TransactionId;
                request.refund_sn = refund_sn;
                request.json = result.Body;
                request.shop_id = store_id;
                //保存交易日志
                await _payAppService.savePayTradeLog(request);
                res.info = "退款成功";
                return res;
            }

            Log.Info("微信退款异常----->" + result.ToJson());
            res.info = "退款异常，请重试";
            return res;
        }

        /// <summary>
        /// 微信轮询查询
        /// </summary>
        /// <param name="config">微信配置</param>
        /// <param name="out_trade_no">商户交易单号</param>
        /// <param name="queryTimes">查询次数(默认10次)</param>
        /// <returns></returns>
        private async Task<WeChatPayOrderQueryResponse> wachatPollQuery(WeChatPayOptions config, string out_trade_no, int queryTimes = 20)
        {
            Log.Info($"开始微信轮询查询----->交易号:{out_trade_no},查询次数:{queryTimes}");
            var response = new WeChatPayOrderQueryResponse();
            //确认支付是否成功,每隔一段时间查询一次订单，共查询10次
            while (queryTimes-- > 0)
            {
                response = await wachatOrderQuery(out_trade_no, config);
                Log.Info($"微信轮询查询结果({10 - queryTimes})----->" + response.ToJson());
                
                // 如果返回失败，直接返回
                if (response.ReturnCode == "FAIL")
                {
                    Log.Info("微信轮询查询请求失败----->" + response.ToJson());
                    break;
                }
                
                // 用户支付中继续查询，则等待3s后继续 
                if (response.TradeState == "USERPAYING" || response.ErrCode == "USERPAYING")
                {
                    Thread.Sleep(3000);
                    continue;
                }
                // 支付成功或其他状态则跳出循环
                else break;
            }
            return response;
        }

        /// <summary>
        /// 微信订单查询
        /// </summary>
        /// <param name="out_trade_no">商户订单号</param>
        /// <param name="config">微信配置</param>
        /// <returns></returns>     
        private async Task<WeChatPayOrderQueryResponse> wachatOrderQuery(string out_trade_no, WeChatPayOptions config)
        {
            Log.Info($"微信订单查询----->交易号:{out_trade_no}");
            var request = new WeChatPayOrderQueryRequest
            {
                OutTradeNo = out_trade_no
            };
            var response = await _weChatPayClient.ExecuteAsync(request, config);
            Log.Info("微信订单查询结果----->" + response.ToJson());
            return response;
        }

        /// <summary>
        /// 微信订单撤销
        /// </summary>
        /// <param name="out_trade_no">商户订单号</param>
        /// <param name="config">配置文件</param>
        /// <returns></returns>
        private async Task wachatOrderReverse(string out_trade_no, WeChatPayOptions config)
        {
            var request = new WeChatPayReverseRequest { OutTradeNo = out_trade_no };
            var response = await _weChatPayClient.ExecuteAsync(request, config);
            WriteLog("微信撤销", request.GetRequestUrl(), request.ToJson(), response.ToJson(), response.Body);
        }
        #endregion

        #region 支付宝支付

        /// <summary>
        /// 支付宝支付
        /// </summary>
        /// <returns></returns>
        private async Task<ApiResult> aliPayQrCode(netPaymentRequset requset)
        {
            Log.Info("支付宝支付请求参数----->" + requset.ToJson());
            var res = new ApiResult(BPM.AspNetCore.Mvc.StatusCode.Fail);
            var alipay_info = await _payAppService.getShopAliPay(requset.shop_id);
            if (alipay_info.IsNull())
            {
                Log.Info("支付宝配置不存在----->" + requset.shop_id);
                res.info = "支付宝配置不存在";
                return res;
            }
            var config = new AlipayOptions();
            config.AppId = alipay_info.alipay_app_id;
            config.AppPrivateKey = alipay_info.merchant_private_key;
            config.AlipayPublicKey = alipay_info.alipay_public_key;
            config.SignType = alipay_info.sign_type;
            
            //支付之前先查一次该交易是否已经支付
            var response = await aliPayQuery(config, requset.payment_sn);
            //表示已经支付过了,防止客户端和接口掉包的情况
            if (response.Code == "10000" && (response.TradeStatus == "TRADE_SUCCESS" || response.TradeStatus == "TRADE_FINISHED"))
            {
                res.Code = (int)BPM.AspNetCore.Mvc.StatusCode.Ok;
                res.info = "支付成功！";
                res.Data = new { TradeState = response.TradeStatus };
                return res;
            }

            // 构建支付请求参数
            var model = new AlipayTradePayModel
            {
                OutTradeNo = requset.payment_sn,
                AuthCode = requset.auth_code,
                TotalAmount = requset.trade_fee,
                Body = requset.attach,
                Subject = requset.attach,
                TerminalId = requset.device_id,
                StoreId = requset.shop_id,
                OperatorId = requset.operator_id,
                Scene = "bar_code", // 支付场景
                ExtendParams = new ExtendParams() { SysServiceProviderId = alipay_info.sys_service_provider_id },
            };

            var payRequset = new AlipayTradePayRequest();
            payRequset.SetBizModel(model);
            var result = await _alipayClient.ExecuteAsync(payRequset, config);
            
            // 记录支付日志
            WriteLog("支付宝支付", payRequset.GetNotifyUrl(), model.ToJson(), result.ToJson(), result.Body);
            
            // 准备交易日志数据
            var request = new payTradeLogDto
            {
                order_id = requset.order_sn,
                trade_type = tardeConstant.pay,
                trade_fee = requset.trade_fee.ToDecimal(), // 使用请求金额而不是响应金额
                pay_code = paymentConstant.aliPay,
                pay_sn = requset.payment_sn,
                trade_no = result.TradeNo,
                json = result.Body,
                qr_code = requset.auth_code,
                shop_id = requset.shop_id
            };

            // 处理支付结果
            switch (result.Code)
            {
                case "10000": // 支付成功
                    res.Code = (int)BPM.AspNetCore.Mvc.StatusCode.Ok;
                    Log.Info("支付宝支付成功----->" + requset.payment_sn);
                    await _payAppService.savePayTradeLog(request);
                    res.info = "交易成功!";
                    res.Data = new { TradeState = "TRADE_SUCCESS" };
                    return res;

                case "10003": // 等待用户付款
                    {
                        Log.Info("支付宝等待用户付款，开始轮询----->" + result.ToJson());
                        var pollResult = await alipayPollQuery(config, requset.payment_sn);
                        
                        if (pollResult.Code == "10000")
                        {
                            switch (pollResult.TradeStatus)
                            {
                                case "TRADE_SUCCESS":
                                case "TRADE_FINISHED":
                                    Log.Info("支付宝支付成功(轮询)----->" + pollResult.ToJson());
                                    res.Code = (int)BPM.AspNetCore.Mvc.StatusCode.Ok;
                                    request.json = pollResult.Body;
                                    request.trade_no = pollResult.TradeNo;
                                    await _payAppService.savePayTradeLog(request);
                                    res.info = "支付成功";
                                    res.Data = new { TradeState = pollResult.TradeStatus };
                                    return res;

                                case "WAIT_BUYER_PAY":
                                    Log.Info("支付宝等待超时----->" + pollResult.ToJson());
                                    res.info = "等待时间超过30秒,请重新扫描支付！";
                                    res.Data = new { TradeState = pollResult.TradeStatus };
                                    return res;

                                case "TRADE_CLOSED":
                                    Log.Info("支付宝交易关闭----->" + pollResult.ToJson());
                                    res.info = "交易已关闭";
                                    res.Data = new { TradeState = pollResult.TradeStatus };
                                    return res;

                                default:
                                    Log.Info("支付宝未知状态----->" + pollResult.ToJson());
                                    res.info = "未知的交易状态：" + pollResult.TradeStatus;
                                    res.Data = new { TradeState = pollResult.TradeStatus };
                                    return res;
                            }
                        }
                        else
                        {
                            Log.Info("支付宝查询失败----->" + pollResult.ToJson());
                            res.info = "支付查询失败：" + pollResult.Msg;
                            res.Data = new { TradeState = "TRADE_CLOSED" };
                            return res;
                        }
                    }

                case "40004": // 业务处理失败
                    Log.Info("支付宝业务处理失败----->" + requset.payment_sn);
                    res.info = "交易失败：" + result.SubMsg;
                    res.Data = new { TradeState = "TRADE_CLOSED" };
                    return res;

                case "20000": // 网关处理失败
                    Log.Info("支付宝网关处理失败----->" + requset.payment_sn);
                    res.info = "支付失败：" + result.Msg;
                    res.Data = new { TradeState = "TRADE_CLOSED" };
                    return res;

                default:
                    Log.Info("支付宝支付失败----->" + requset.payment_sn);
                    res.info = result.Msg + (result.SubMsg ?? "");
                    res.Data = new { TradeState = "TRADE_CLOSED" };
                    return res;
            }
        }
        /// <summary>
        /// 支付宝轮询查询
        /// </summary>
        /// <param name="config">微信配置</param>
        /// <param name="out_trade_no">商户交易单号</param>
        /// <param name="queryTimes">查询次数(默认10次)</param>
        /// <returns></returns>
        private async Task<AlipayTradeQueryResponse> alipayPollQuery(AlipayOptions config, string out_trade_no, int queryTimes = 20)
        {
            var response = new AlipayTradeQueryResponse();
            //2）不能确定是否失败，需查单
            //用商户订单号去查单
            //确认支付是否成功,每隔一段时间查询一次订单，共查询10次
            while (queryTimes-- > 0)
            {
                response = await aliPayQuery(config, out_trade_no);
                if (response.Code != "10000")
                {
                    // 查询失败，等待后重试
                    Thread.Sleep(3000);
                    continue;
                }

                // 用户支付中继续查询，则等待2s后继续 
                if (response.TradeStatus == "WAIT_BUYER_PAY")
                {
                    Thread.Sleep(3000);
                    continue;
                }
                else break;
            }
            return response;
        }

        /// <summary>
        /// 支付宝支付
        /// </summary>
        /// <returns></returns>
        private async Task<AlipayTradeQueryResponse> aliPayQuery(AlipayOptions config, string out_trade_no)
        {
            var model = new AlipayTradeQueryModel
            {
                OutTradeNo = out_trade_no
            };
            var req = new AlipayTradeQueryRequest();
            req.SetBizModel(model);
            return await _alipayClient.ExecuteAsync(req, config);
        }

        /// <summary>
        /// 支付宝退款
        /// </summary>
        /// <param name="store_id">门店编号</param>
        /// <param name="order_sn">订单编号</param>
        /// <param name="payment_sn">交易单号</param>
        /// <param name="refund_sn">退款单号</param>
        /// <param name="refund_fee">退款金额(元)</param>
        /// <returns></returns>
        private async Task<ApiResult> alipayRefund(string store_id, string order_sn, string payment_sn, string refund_sn, decimal refund_fee)
        {
            Log.Info($"支付宝退款请求参数----->店铺ID:{store_id},订单号:{order_sn},交易号:{payment_sn},退款单号:{refund_sn},退款金额:{refund_fee}");
            var res = new ApiResult(BPM.AspNetCore.Mvc.StatusCode.Fail);
            
            // 获取支付宝配置
            var alipay_info = await _payAppService.getShopAliPay(store_id);
            if (alipay_info.IsNull())
            {
                Log.Info("支付宝配置不存在----->" + store_id);
                res.info = "支付宝配置不存在！";
                return res;
            }

            // 初始化支付宝配置
            var config = new AlipayOptions();
            config.AppId = alipay_info.alipay_app_id;
            config.AppPrivateKey = alipay_info.merchant_private_key;
            config.AlipayPublicKey = alipay_info.alipay_public_key;
            config.SignType = alipay_info.sign_type;

            // 查询原交易信息
            var queryResponse = await aliPayQuery(config, payment_sn);
            if (queryResponse.Code != "10000")
            {
                Log.Info("支付宝原交易查询失败----->" + queryResponse.ToJson());
                res.info = "原交易查询失败：" + queryResponse.Msg;
                return res;
            }

            // 检查交易状态是否可退款
            if (queryResponse.TradeStatus == "TRADE_FINISHED")
            {
                Log.Info("支付宝交易已完结不可退款----->" + queryResponse.ToJson());
                res.info = "交易已完结，不可退款";
                return res;
            }
            if (queryResponse.TradeStatus != "TRADE_SUCCESS")
            {
                Log.Info("支付宝交易状态不正确----->" + queryResponse.ToJson());
                res.info = "交易状态不正确，当前状态：" + queryResponse.TradeStatus;
                return res;
            }

            // 构建退款请求参数
            var model = new AlipayTradeRefundModel
            {
                OutTradeNo = payment_sn,
                RefundAmount = refund_fee.ToString(),
                OutRequestNo = refund_sn,
                RefundReason = "正常退款",
                StoreId = store_id
            };

            var refundRequest = new AlipayTradeRefundRequest();
            refundRequest.SetBizModel(model);
            
            // 发起退款请求
            var result = await _alipayClient.ExecuteAsync(refundRequest, config);
            
            // 记录退款日志
            WriteLog("支付宝退款", refundRequest.GetNotifyUrl(), model.ToJson(), result.ToJson(), result.Body);

            // 准备退款日志数据
            var request = new payTradeLogDto
            {
                order_id = order_sn,
                trade_type = tardeConstant.refund,
                trade_fee = refund_fee,
                pay_code = paymentConstant.aliPay,
                pay_sn = payment_sn,
                refund_sn = refund_sn,
                trade_no = result.TradeNo,
                json = result.Body,
                shop_id = store_id
            };

            // 处理退款结果
            switch (result.Code)
            {
                case "10000": // 退款成功
                    if (result.FundChange == "Y") // 资金有变动，说明退款成功
                    {
                        Log.Info("支付宝退款成功----->" + result.ToJson());
                        res.Code = (int)BPM.AspNetCore.Mvc.StatusCode.Ok;
                        await _payAppService.savePayTradeLog(request);
                        res.info = "退款成功";
                        return res;
                    }
                    else
                    {
                        Log.Info("支付宝退款资金未变动----->" + result.ToJson());
                        res.info = "退款请求成功但资金未变动，请检查退款状态";
                        return res;
                    }

                case "20000": // 网关错误
                    Log.Info("支付宝退款网关错误----->" + result.ToJson());
                    res.info = "退款请求失败：" + result.Msg;
                    return res;

                case "40004": // 业务处理失败
                    Log.Info("支付宝退款业务失败----->" + result.ToJson());
                    res.info = "退款失败：" + result.SubMsg;
                    return res;

                default:
                    Log.Info("支付宝退款异常----->" + result.ToJson());
                    res.info = "退款异常：" + result.Msg + (result.SubMsg ?? "");
                    return res;
            }
        }

        #endregion


        /// <summary>
        /// 保存交易日志
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        private async Task savePayTradeLog(payTradeLogDto request)
        {
            //保存交易日志
            await _payAppService.savePayTradeLog(request);
        }

        /// <summary>
        /// 日志记录
        /// </summary>
        /// <param name="title">标题</param>
        /// <param name="pay_way">支付网关</param>
        /// <param name="requestData">请求数据</param>
        /// <param name="resultData">响应数据</param>
        /// <param name="rawData">原始响应</param>
        private void WriteLog(string title, string pay_way, string requestData, string resultData, string rawData)
        {
            var content = new StringBuilder();
            content.AppendLine($"支付设备:{device_info}");
            content.AppendLine($"支付网关:{pay_way}");
            content.AppendLine($"请求参数:{requestData}");
            content.AppendLine($"返回结果:{resultData}");
            content.AppendLine($"原始响应:{rawData}");
            Log.Set<LogContent>(p => p.Class = GetType().FullName)
                .Set<LogContent>(p => p.Caption = title)
                .Set<LogContent>(p => p.Content = content)
               .Info();
        }

        #endregion
    }
}