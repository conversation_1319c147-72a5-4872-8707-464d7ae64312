﻿using Api.Filter;

namespace Web.Api.Controllers
{

    /// <summary>
    ///  商品控制器
    /// </summary>
    [Route("api/product")]
    [Authorize]
    public class productController : ApiControllerBase
    {
        /// <summary>
        /// 商品应用服务
        /// </summary>
        private readonly IProdcutAppService _prodcutAppService;

        /// <summary>
        /// 初始化服务
        /// </summary>
        /// <param name="prodcutAppService">应用服务</param>
        public productController(IProdcutAppService prodcutAppService)
        {
            _prodcutAppService = prodcutAppService;
        }

        /// <summary>
        /// 获取plu 商品
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns></returns>
        [HttpPost, Route("get_plu_list")]
        public async Task<IActionResult> getPagePluList([FromBody] pluRequest request)
        {
            var query = new pluQuery();
            query.Page = request.page_index;
            query.PageSize = request.page_size;
            query.Order = "PLUCODE DESC";
            query.shop_id = request.shop_id;
            var result = await _prodcutAppService.getPagePluList(query);
            var jsonData = new
            {
                data = result.Data, //数据
                total_page = result.PageCount,//总页数
                page = result.Page,//当前页
                records = result.TotalCount,//总记录数
            };
            return Success(jsonData);
        }


        /// <summary>
        /// 获取库存
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns></returns>
        [HttpPost, Route("get_stock_list")]
        public async Task<IActionResult> getPageStockList([FromBody] stockRequest request)
        {
            var query = new stockQuery();
            query.Page = request.page_index;
            query.PageSize = request.page_size;
            query.shop_id = request.shop_id;
            query.Order = "PLUCODE DESC";
            var result = await _prodcutAppService.getPageStockList(query);
            var jsonData = new
            {
                data = result.Data, //数据
                total_page = result.PageCount,//总页数
                page = result.Page,//当前页
                records = result.TotalCount,//总记录数
            };
            return Success(jsonData);
        }


        /// <summary>
        /// 获取品牌商品
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns></returns>
        [HttpPost, Route("get_brand_list")]
        public async Task<IActionResult> getPagePluList([FromBody] baseRequest request)
        {
            var result = await _prodcutAppService.getPageBrandList(new baseQuery(request.page_index, request.page_size, "BRAND DESC"));
            var jsonData = new
            {
                data = result.Data, //数据
                total_page = result.PageCount,//总页数
                page = result.Page,//当前页
                records = result.TotalCount,//总记录数
            };
            return Success(jsonData);
        }

        /// <summary>
        /// 获取分类商品
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns></returns>
        [HttpPost, Route("get_class_list")]
        public async Task<IActionResult> getPageClassList([FromBody] baseRequest request)
        {
            var result = await _prodcutAppService.getPageClassList(new baseQuery(request.page_index, request.page_size, "CLSCODE DESC"));
            var jsonData = new
            {
                data = result.Data, //数据
                total_page = result.PageCount,//总页数
                page = result.Page,//当前页
                records = result.TotalCount,//总记录数
            };
            return Success(jsonData);
        }


        /// <summary>
        /// 获取分类商品
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns></returns>
        [HttpPost, Route("get_display_list")]
        public async Task<IActionResult> getPageDisplayList([FromBody] displayRequest request)
        {
            var query = new baseQuery(request.page_index, request.page_size, "DSPCODE DESC");
            query.shop_id = request.shop_id;
            var result = await _prodcutAppService.getPageDisplayList(query);
            var jsonData = new
            {
                data = result.Data, //数据
                total_page = result.PageCount,//总页数
                page = result.Page,//当前页
                records = result.TotalCount,//总记录数
            };
            return Success(jsonData);
        }

        /// <summary>
        /// 获取部类商品
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns></returns>
        [HttpPost, Route("get_department_list")]
        public async Task<IActionResult> getPageDepartmentList([FromBody] baseRequest request)
        {
            var result = await _prodcutAppService.getPageDepartmentList(new baseQuery(request.page_index, request.page_size, "DPTCODE DESC"));
            var jsonData = new
            {
                data = result.Data, //数据
                total_page = result.PageCount,//总页数
                page = result.Page,//当前页
                records = result.TotalCount,//总记录数
            };
            return Success(jsonData);
        }

        /// <summary>
        /// 获取代收
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns></returns>
        [HttpPost, Route("get_ra_list")]
        [NoSignAttribute, AllowAnonymous]
        public async Task<IActionResult> getPageRaList([FromBody] baseRequest request)
        {
            var query = new baseFreeSqlQuery(request.page_index, request.page_size, "RACODE DESC");
            var data = await _prodcutAppService.getPageRaList(query);
            var jsonData = new
            {
                data = data, //数据
                total_page = query.GetPageCount(),//总页数
                page = query.PageNumber,//当前页
                records = query.Count,//总记录数
            };
            return Success(jsonData);
        }




    }
}