﻿namespace Web.Api.Controllers
{

    /// <summary>
    ///  门店控制器
    /// </summary>
    [Route("api/shop")]
    [Authorize]
    public class shopController : ApiControllerBase
    {
        /// <summary>
        /// 门店应用服务
        /// </summary>
        private readonly IShopAppService _shopAppService;

        /// <summary>
        /// 初始化服务
        /// </summary>
        /// <param name="shopAppService">应用服务</param>
        public shopController(IShopAppService shopAppService)
        {
            _shopAppService = shopAppService;
        }

        /// <summary>
        /// 获取终端信息
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns></returns>
        [HttpPost, Route("get_terminal_info")]
        public async Task<IActionResult> getTerminalInfo([FromBody] terminalRequest request)
        {
            var data = await _shopAppService.getTerminalInfo(request);
            if (data.IsNull())
                return Fail("终端记录不存在！");
            return Success(data);
        }


    }
}