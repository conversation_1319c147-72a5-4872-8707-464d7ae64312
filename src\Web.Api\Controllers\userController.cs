﻿using Api.Filter;

namespace Web.Api.Controllers
{

    /// <summary>
    ///  用户控制器
    /// </summary>
    [Route("api/user")]
    [Authorize]
    public class userController : ApiControllerBase
    {
        /// <summary>
        /// 用户应用服务
        /// </summary>
        private readonly IUserAppService _userAppService;

        /// <summary>
        /// 订单服务
        /// </summary>
        private readonly IOrderAppService _orderAppService;

        /// <summary>
        /// 初始化服务
        /// </summary>
        /// <param name="prodcutAppService">应用服务</param>
        public userController(IUserAppService userAppService, IOrderAppService orderAppService)
        {
            _userAppService = userAppService;
            _orderAppService = orderAppService;
        }

        /// <summary>
        /// 获取用户
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns></returns>
        [HttpPost, Route("get_user_list")]
        public async Task<IActionResult> getPageUserList([FromBody] baseRequest request)
        {
            var result = await _userAppService.getPageUserList(new baseQuery(request.page_index, request.page_size, "CODE DESC"));
            var jsonData = new
            {
                data = result.Data, //数据
                total_page = result.PageCount,//总页数
                page = result.Page,//当前页
                records = result.TotalCount,//总记录数
            };
            return Success(jsonData);
        }

        /// <summary>
        /// 上传用户日志
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns></returns>
        [NoSign, HttpPost, Route("upload_user_logs")]
        public async Task<IActionResult> saveUserLogs([FromBody] userLogsRequest request)
        {
            var number= await _userAppService.saveUserLogs(request);
            var jsonData = new { number = number };
            return Success(jsonData);
        }

        /// <summary>
        ///  获取系统时间
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        //[HttpPost, Route("get_system_time")]
        [NoSign, HttpPost, Route("get_system_time")]
        public IActionResult getSystemTime(signRequestBase request)
        {
            var jsonData = new { system_time = DateTime.Now };
            return Success(jsonData);
        }
    }
}