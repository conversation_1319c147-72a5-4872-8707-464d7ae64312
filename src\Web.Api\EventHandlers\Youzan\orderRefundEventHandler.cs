﻿using BPM.DependencyInjection;
using BPM.Extras.Youzan.Request;
using BPM.Extras.Youzan.Responses;
using BPM.Extras.Youzan.Services;
using BPM.Logs;
using BPM.Logs.Contents;
using Domain.Models.sync;
using Domain.order.Repository;
using DotNetCore.CAP;
using EventHandlers;
using System.Collections.Generic;
using static BPM.Extras.Youzan.Request.orderRefundRequest;
using static BPM.Extras.Youzan.Request.orderTradeRequest;
using static Web.Api.EventHandlers.Youzan.orderTradeMessageEvent;

namespace Web.Api.EventHandlers.Youzan;

/// <summary>
/// 订单退款消息事件
/// </summary>
public class orderRefundEventHandler : MessageEventHandlerBase, ITransientDependency
{
    /// <summary>
    /// 订单应用接口
    /// </summary>
    private readonly IOrderAppService _orderAppService;

    /// <summary>
    /// 商品应用接口
    /// </summary>
    private readonly IProdcutAppService _prodcutAppService;

    /// <summary>
    /// 门店应用接口
    /// </summary>
    private readonly IShopAppService _shopAppService;

    /// <summary>
    /// 有赞服务接口
    /// </summary>
    private readonly IYouzanService _youzanService;

    /// <summary>
    /// 日志服务
    /// </summary>
    private readonly ILog _log;

    /// <summary>
    /// 订单仓储接口
    /// </summary>
    private readonly IOrderRepository _orderRepository;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="orderRepository"></param>
    public orderRefundEventHandler(IOrderAppService orderAppService, IProdcutAppService prodcutAppService
        , IShopAppService shopAppService, IYouzanService youzanService, ILog log, IOrderRepository orderRepository)
    {
        _orderAppService = orderAppService;
        _prodcutAppService = prodcutAppService;
        _shopAppService = shopAppService;
        _youzanService = youzanService;
        _log = log;
        _orderRepository = orderRepository;
    }

    /// <summary>
    /// 订单事件总线
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [CapSubscribe("orderRefundEvent", Group = "ipos.order.group")]
    public async Task orderRefundEvent(tradeMessage message)
    {
        try
        {
            _log.Info($"【退款处理】开始处理退款事件，订单号:{message.order_sn}，线程ID:{Thread.CurrentThread.ManagedThreadId}，消费者组:ipos.order.group");
            var orderItems = await _orderAppService.getshopTxnsList(message.order_sn);
            // 订单主体
            // 订单原价
            var origin_price = orderItems.Where(x => x.TXNTYPE == 1 && x.SALESTYPE < 6).Sum(x => x.ORGAMT);
            //订单销售价
            var current_price = orderItems.Where(x => x.TXNTYPE == 1 && x.SALESTYPE < 6).Sum(x => x.ORGAMT + x.ITEMDISC + x.TTLDISC);

            var shop_info = await _shopAppService.getShopInfoById(orderItems[0].SHOPID);
            // 基础信息
            var request = new orderRefundRequest()
            {
                out_order_no = message.order_sn,
                out_order_time = orderItems[0].TXNTIME,
                out_order_price = current_price * -1,
                origin_kdt_id = shop_info.source_no.ToLong()
            };
            // 买家信息
            if (!orderItems[0].PHONE.IsEmpty())
            {
                request.user = new orderRefundRequest.userInfo()
                {
                    account_id = orderItems[0].PHONE
                };
            }
            var trade_items = new List<refundItems>();
            foreach (var item in orderItems.Where(x => x.TXNTYPE == 1 && x.SALESTYPE < 6))
            {
                var product_info = await _prodcutAppService.getProductSkuInfo(item.CODE);
                if (!product_info.IsNull())
                {
                    trade_items.Add(new refundItems()
                    {
                        item_id = product_info.product_id,
                        // 数量
                        num = item.QTY * -1,
                        sku_id = product_info.id,
                        barcode = product_info.id,
                        // 商品名称
                        title = product_info.sku_name,
                        out_order_item_id = item.TXNID,
                        // 实付金额
                        current_unit_price = (item.ORGAMT + item.ITEMDISC + item.TTLDISC) * -1,
                        // 商品原始金额
                        origin_unit_price = item.ORGAMT * -1,
                        // 实付总金额
                        current_total_price = (item.ORGAMT + item.ITEMDISC + item.TTLDISC) * item.QTY,
                        // 原始总金额
                        origin_total_price = item.ORGAMT * item.QTY
                    });
                }
                else
                {
                    var plu_info = await _prodcutAppService.getPluInfo(item.CODE);
                    if (plu_info != null)
                    {
                        trade_items.Add(new refundItems()
                        {
                            barcode = item.CODE,
                            // 数量
                            num = item.QTY * -1,
                            // 商品名称
                            title = plu_info.TITLE,
                            out_order_item_id = item.TXNID,
                            // 实付金额
                            current_unit_price = (item.ORGAMT + item.ITEMDISC + item.TTLDISC) * -1,
                            // 商品原始金额
                            origin_unit_price = item.ORGAMT * -1,
                            // 实付总金额
                            current_total_price = (item.ORGAMT + item.ITEMDISC + item.TTLDISC) * item.QTY,
                            // 原始总金额
                            origin_total_price = item.ORGAMT * item.QTY
                        });
                    }
                }
            }
            request.items = trade_items;
            var parameter = new YouzanParameter();
            parameter.url = $"/api/youzan.crm.out.order.create/2.0.0";
            parameter.body = request;
            var response = await _youzanService.getYouzanData(parameter);
            WriteLog("【退款处理】同步POS售后单", parameter.url, parameter.body.ToJson(), response.ToJson());

            // 记录同步日志
            var syncLog = new syncLogsEntity
            {
                Type = "REFUND",
                ShopId = orderItems[0].SHOPID,
                DeviceId = orderItems[0].TERMINAL,
                SourceId = message.order_sn,
                SyncStatus = response.code.ToInt(),
                Body = response.ToJson()
            };
            await _orderRepository.saveSyncLogs(syncLog);

            // 检查接口调用结果
            if (!response.success)
            {
                _log.Error($"【退款处理】接口调用失败，单号:{message.order_sn}，错误:{response.message}");
                //return;
            }
            else
            {
                _log.Error($"【退款处理】接口调用成功，单号:{message.order_sn}，消息:{response.message}");
            }

            if (!orderItems[0].PHONE.IsEmpty())
            {
                // 计算退款积分
                var cpts = orderItems.Where(x => x.TXNTYPE == 1 && x.SALESTYPE < 6)
                    .Sum(s => s.CPTS > 0 ? ((s.ORGAMT + s.ITEMDISC + s.TTLDISC) * -0.01) / s.CPTS : 0);
                if (cpts > 0)
                {
                    // 同步积分
                    var pointRequset = new customerPointRequest();
                    pointRequset.reason = $"门店退货减少积分-{orderItems[0].TXNNUM}";
                    pointRequset.source_kdt_id = request.origin_kdt_id;
                    pointRequset.points = (int)Math.Round(cpts);
                    pointRequset.biz_value = orderItems[0].TXNNUM;
                    pointRequset.user = new customerPointRequest.userInfo()
                    {
                        account_id = orderItems[0].PHONE
                    };
                    await syncPoint(pointRequset);
                }
            }
           
        }
        catch (Exception ex)
        {
            _log.Error($"【退款处理】处理退款事件时发生异常，订单号:{message.order_sn}，异常信息:{ex.Message}，堆栈:{ex.StackTrace}");
        }
    }

    /// <summary>
    /// 同步积分
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    private async Task<ApiResult> syncPoint(customerPointRequest request)
    {
        var parameter = new YouzanParameter();
        parameter.url = $"/api/youzan.crm.customer.points.decrease/4.0.0";
        var dict = new Dictionary<string, object>();
        dict.Add("params", request);
        parameter.body = dict;
        var response = await _youzanService.getYouzanData(parameter);
        WriteLog("减少客户积分", parameter.url, parameter.body.ToJson(), response.ToJson());
        if (response.success)
        {
            var result = response.data.ToString().ToObject<customerPointResponse>();
            return new ApiResult(StatusCode.Ok);
        }
        return new ApiResult(StatusCode.Fail, response.message);
    }

    /// <summary>
    /// 日志记录
    /// </summary>
    /// <param name="title">标题</param>
    /// <param name="pay_way">支付网关</param>
    /// <param name="requestData">请求数据</param>
    /// <param name="rawData">原始响应</param>
    private void WriteLog(string title, string pay_way, string requestData, string resultData)
    {
        var content = new StringBuilder();
        content.AppendLine($"请求地址:{pay_way}");
        content.AppendLine($"请求参数:{requestData}");
        content.AppendLine($"返回结果:{resultData}");
        _log.Set<LogContent>(p => p.Class = GetType().FullName)
            .Set<LogContent>(p => p.Caption = title)
            .Set<LogContent>(p => p.Content = content)
           .Info();
    }

    /// <summary>
    /// 历史订单退款事件总线 - 不计算积分
    /// </summary>
    /// <param name="message">消息内容</param>
    /// <returns></returns>
    [CapSubscribe("orderRefundHistoryEvent")]
    public async Task orderRefundHistoryEvent(tradeMessage message)
    {
        var orderItems = await _orderAppService.getshopTxnsList(message.order_sn);
        // 订单原价
        var origin_price = orderItems.Where(x => x.TXNTYPE == 1 && x.SALESTYPE < 6).Sum(x => x.ORGAMT);
        //订单销售价
        var current_price = orderItems.Where(x => x.TXNTYPE == 1 && x.SALESTYPE < 6).Sum(x => x.ORGAMT + x.ITEMDISC + x.TTLDISC);

        var shop_info = await _shopAppService.getShopInfoById(orderItems[0].SHOPID);
        // 基础信息
        var request = new orderRefundRequest()
        {
            out_order_no = message.order_sn,
            out_order_time = orderItems[0].TXNTIME,
            out_order_price = current_price * -1,
            origin_kdt_id = shop_info.source_no.ToLong()
        };
        // 买家信息
        if (!orderItems[0].PHONE.IsEmpty())
        {
            request.user = new orderRefundRequest.userInfo()
            {
                account_id = orderItems[0].PHONE
            };
        }
        var trade_items = new List<refundItems>();
        foreach (var item in orderItems.Where(x => x.TXNTYPE == 1 && x.SALESTYPE < 6))
        {
            var product_info = await _prodcutAppService.getProductSkuInfo(item.CODE);
            if (!product_info.IsNull())
            {
                trade_items.Add(new refundItems()
                {
                    item_id = product_info.product_id,
                    // 数量
                    num = item.QTY * -1,
                    sku_id = product_info.id,
                    barcode = product_info.id,
                    // 商品名称
                    title = product_info.sku_name,
                    out_order_item_id = item.TXNID,
                    // 实付金额
                    current_unit_price = (item.ORGAMT + item.ITEMDISC + item.TTLDISC) * -1,
                    // 商品原始金额
                    origin_unit_price = item.ORGAMT * -1,
                    // 实付总金额
                    current_total_price = (item.ORGAMT + item.ITEMDISC + item.TTLDISC) * item.QTY,
                    // 原始总金额
                    origin_total_price = item.ORGAMT * item.QTY
                });
            }
            else
            {
                var plu_info = await _prodcutAppService.getPluInfo(item.CODE);
                if (plu_info != null)
                {
                    trade_items.Add(new refundItems()
                    {
                        barcode = item.CODE,
                        // 数量
                        num = item.QTY * -1,
                        // 商品名称
                        title = plu_info.TITLE,
                        out_order_item_id = item.TXNID,
                        // 实付金额
                        current_unit_price = (item.ORGAMT + item.ITEMDISC + item.TTLDISC) * -1,
                        // 商品原始金额
                        origin_unit_price = item.ORGAMT * -1,
                        // 实付总金额
                        current_total_price = (item.ORGAMT + item.ITEMDISC + item.TTLDISC) * item.QTY,
                        // 原始总金额
                        origin_total_price = item.ORGAMT * item.QTY
                    });
                }
            }
        }
        request.items = trade_items;
        var parameter = new YouzanParameter();
        parameter.url = $"/api/youzan.crm.out.order.create/2.0.0";
        parameter.body = request;
        var response = await _youzanService.getYouzanData(parameter);
        WriteLog("同步历史POS售后单", parameter.url, parameter.body.ToJson(), response.ToJson());

        // 记录同步日志
        var syncLog = new syncLogsEntity
        {
            Type = "REFUND",
            ShopId = orderItems[0].SHOPID,
            DeviceId = orderItems[0].TERMINAL,
            SourceId = message.order_sn,
            SyncStatus = response.code.ToInt(),
            Body = response.ToJson()
        };
        await _orderRepository.saveSyncLogs(syncLog);
    }

}
