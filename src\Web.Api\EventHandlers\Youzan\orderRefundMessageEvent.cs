﻿using BPM.Events.Messages;
using static Web.Api.EventHandlers.Youzan.orderRefundMessageEvent;

namespace Web.Api.EventHandlers.Youzan;

/// <summary>
///  订单请求消息事件
/// </summary>
public class orderRefundMessageEvent : MessageEvent<refundMessage>
{
    /// <summary>
    /// 初始化一个<see cref="orderTradeMessageEvent"/>类型的实例
    /// </summary>
    /// <param name="data">数据</param>
    public orderRefundMessageEvent(refundMessage data) : base(data)
    {
        Send = true;
        Name = "orderRefundEvent";
    }

    /// <summary>
    /// 测试消息
    /// </summary>
    public class refundMessage
    {
        /// <summary>
        /// 订单号
        /// </summary>
        public string order_sn { get; set; }
    }
}

/// <summary>
/// 历史退款订单消息事件 - 不计算积分
/// </summary>
public class orderRefundHistoryMessageEvent : MessageEvent<refundMessage>
{
    /// <summary>
    /// 初始化一个<see cref="orderRefundHistoryMessageEvent"/>类型的实例
    /// </summary>
    /// <param name="data">数据</param>
    public orderRefundHistoryMessageEvent(refundMessage data) : base(data)
    {
        Send = true;
        Name = "orderRefundHistoryEvent";
    }
}