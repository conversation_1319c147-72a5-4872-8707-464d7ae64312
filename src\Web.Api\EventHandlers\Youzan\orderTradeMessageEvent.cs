﻿using BPM.Events.Messages;
using static Web.Api.EventHandlers.Youzan.orderTradeMessageEvent;

namespace Web.Api.EventHandlers.Youzan;

/// <summary>
///  订单请求消息事件
/// </summary>
public class orderTradeMessageEvent : MessageEvent<tradeMessage>
{
    /// <summary>
    /// 初始化一个<see cref="orderTradeMessageEvent"/>类型的实例
    /// </summary>
    /// <param name="data">数据</param>
    public orderTradeMessageEvent(tradeMessage data) : base(data)
    {
        Send = true;
        Name = "orderEvent";
    }

    /// <summary>
    /// 测试消息
    /// </summary>
    public class tradeMessage
    {
        /// <summary>
        /// 订单号
        /// </summary>
        public string order_sn { get; set; }
    }
}

/// <summary>
/// 历史订单消息事件 - 不计算积分
/// </summary>
public class orderTradeHistoryMessageEvent : MessageEvent<tradeMessage>
{
    /// <summary>
    /// 初始化一个<see cref="orderTradeHistoryMessageEvent"/>类型的实例
    /// </summary>
    /// <param name="data">数据</param>
    public orderTradeHistoryMessageEvent(tradeMessage data) : base(data)
    {
        Send = true;
        Name = "orderHistoryEvent";
    }
}