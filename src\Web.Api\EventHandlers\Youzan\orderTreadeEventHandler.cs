using BPM.DependencyInjection;
using BPM.Extras.Youzan.Request;
using BPM.Extras.Youzan.Responses;
using BPM.Extras.Youzan.Services;
using BPM.Logs;
using BPM.Logs.Contents;
using Domain.Models.sync;
using Domain.order.Repository;
using DotNetCore.CAP;
using EventHandlers;
using System.Collections.Generic;
using static BPM.Extras.Youzan.Request.customerPointRequest;
using static BPM.Extras.Youzan.Request.orderTradeRequest;
using static Web.Api.EventHandlers.Youzan.orderTradeMessageEvent;

namespace Web.Api.EventHandlers.Youzan;

public class orderTreadeEventHandler : MessageEventHandlerBase, ITransientDependency
{
    /// <summary>
    /// 订单应用接口
    /// </summary>
    private readonly IOrderAppService _orderAppService;

    /// <summary>
    /// 商品应用接口
    /// </summary>
    private readonly IProdcutAppService _prodcutAppService;

    /// <summary>
    /// 门店应用接口
    /// </summary>
    private readonly IShopAppService _shopAppService;

    /// <summary>
    /// 会员应用服务
    /// </summary>
    private readonly IMemberAppService _memberAppService;


    /// <summary>
    /// 有赞服务接口
    /// </summary>
    private readonly IYouzanService _youzanService;

    /// <summary>
    /// 日志服务
    /// </summary>
    private readonly ILog _log;

    /// <summary>
    /// 订单仓储接口
    /// </summary>
    private readonly IOrderRepository _orderRepository;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="orderRepository"></param>
    public orderTreadeEventHandler(IOrderAppService orderAppService, IProdcutAppService prodcutAppService
        , IShopAppService shopAppService, IYouzanService youzanService
        , ILog log, IMemberAppService memberAppService, IOrderRepository orderRepository)
    {
        _orderAppService = orderAppService;
        _prodcutAppService = prodcutAppService;
        _shopAppService = shopAppService;
        _youzanService = youzanService;
        _log = log;
        _memberAppService = memberAppService;
        _orderRepository = orderRepository;
    }

    /// <summary>
    /// 订单事件总线 - 处理订单同步到有赞系统
    /// </summary>
    /// <param name="message">订单消息体，包含订单基本信息</param>
    /// <returns></returns>
    [CapSubscribe("orderEvent", Group = "ipos.order.group")]
    public async Task orderEvent(tradeMessage message)
    {
        try
        {
            // 记录开始处理订单的日志
            _log.Info($"【订单处理】开始处理订单，订单号:{message.order_sn}");

            // 初始化有赞订单请求对象
            var request = new orderTradeRequest();
            // 根据订单号获取订单明细列表
            var orderItems = await _orderAppService.getshopTxnsList(message.order_sn);

            // 检查订单数据是否存在
            if (orderItems == null || orderItems.Count == 0)
            {
                _log.Error($"【订单处理】未找到订单数据，订单号:{message.order_sn}");
                return;
            }

            // 设置订单基本信息（订单号、创建时间、支付时间）
            request.main_info = new orderTradeRequest.oderBaseInfo()
            {
                out_biz_no = message.order_sn,                    // 外部订单号
                create_time = orderItems[0].TXNTIME,              // 订单创建时间
                pay_time = orderItems[orderItems.Count - 1].TXNTIME > DateTime.Now 
                    ? DateTime.Now 
                    : orderItems[orderItems.Count - 1].TXNTIME, // 支付时间（如大于当前时间则用系统时间）
                order_status = "SUCCESS",                         // 订单状态：交易成功
                open_source = new OpenSource                      // 订单来源
                {
                    order_status = "SUCCESS",                     // 订单状态：交易成功
                    trade_mark = "POS",                          // 交易终端：POS机
                    trade_channel = new TradeChannel             // 交易渠道
                    {
                        common_channel = "POS"                    // 通用渠道：线下门店
                    }
                }
            };

            // 计算订单价格信息
            // 计算原价（只计算普通销售类型的商品）
            var origin_price = orderItems.Where(x => x.TXNTYPE == 0 && x.SALESTYPE < 6).Sum(x => x.ORGAMT);
            // 计算实际销售价（原价 + 商品折扣 + 整单折扣）
            var current_price = orderItems.Where(x => x.TXNTYPE == 0 && x.SALESTYPE < 6).Sum(x => x.ORGAMT + x.ITEMDISC + x.TTLDISC);
            // 设置订单价格信息
            request.order_price = new orderPrice()
            {
                current_price = current_price,          // 当前价格
                origin_price = origin_price,            // 原价
                total_price = current_price,            // 总价
                promotion_amount = origin_price - current_price  // 优惠金额
            };

            // 处理买家信息
            if (!orderItems[0].PHONE.IsEmpty())
            {
                // 根据手机号获取会员信息
                var customer_info = await _memberAppService.getCustomerInfo(orderItems[0].PHONE);
                var yz_open_id = customer_info?.open_id;

                // 如果customer_info.open_id为空，用手机号码调用有赞API获取open_id
                if (string.IsNullOrEmpty(yz_open_id))
                {
                    yz_open_id = await getYzOpenIdByPhone(orderItems[0].PHONE);
                    _log.Info($"【订单处理】通过有赞API获取openId，订单号:{message.order_sn}，手机号:{orderItems[0].PHONE}，openId:{yz_open_id}");
                }

                // 如果yz_open_id仍然为空，则不处理订单
                if (string.IsNullOrEmpty(yz_open_id))
                {
                    _log.Warn($"【订单处理】无法获取有效的openId，跳过订单处理，订单号:{message.order_sn}，手机号:{orderItems[0].PHONE}");
                    return;
                }

                // 设置买家信息
                request.buyer = new buyerInfo()
                {
                    tel = orderItems[0].PHONE,
                    yz_open_id = yz_open_id
                };
                _log.Info($"【订单处理】会员信息处理完成，订单号:{message.order_sn}，openId:{yz_open_id}");
            }

            // 处理订单商品信息
            var trade_items = new List<tradeItems>();
            // 遍历订单中的商品（只处理普通销售类型的商品）
            foreach (var item in orderItems.Where(x => x.TXNTYPE == 0 && x.SALESTYPE < 6))
            {
                try
                {
                    // 获取商品SKU信息（含条码）
                    var product_info = await _prodcutAppService.getProductSkuInfoWithBarcodeByChannel0(item.CODE);
                    if (!product_info.IsNull())
                    {

                        // 添加商品明细
                        trade_items.Add(new tradeItems()
                        {
                            num = item.QTY,                                                             //商品数量
                            origin_price = item.ORGAMT / item.QTY,                                      //商品原始价格（单位：分）
                            current_price = (item.ORGAMT + item.ITEMDISC + item.TTLDISC) / item.QTY,    //商品当前价格（单位：分）
                            current_total_amount = item.ORGAMT + item.ITEMDISC + item.TTLDISC,          //商品订单实付金额（单位：分）
                            goods_info = new tradeItems.goodsInfo
                            {
                                item_no = product_info.bar_code,
                                title = product_info.sku_name,
                                sku_no = product_info.has_sku == 1 ? product_info.sku_sn : null
                            }
                        });
                    }
                    else
                    {
                        // 如果未找到SKU信息，尝试获取PLU信息
                        var plu_info = await _prodcutAppService.getPluInfo(item.CODE);
                        if (plu_info != null)
                        {
                            // 如果找到PLU信息，添加商品明细
                            trade_items.Add(new tradeItems()
                            {
                                num = item.QTY,                                                     //商品数量
                                origin_price = item.ORGAMT / item.QTY,                                //商品原始价格（单位：分）
                                current_price = (item.ORGAMT + item.ITEMDISC + item.TTLDISC) / item.QTY, //商品当前价格（单位：分）
                                current_total_amount = item.ORGAMT + item.ITEMDISC + item.TTLDISC,  //商品订单实付金额（单位：分）
                                goods_info = new tradeItems.goodsInfo()
                                {
                                    item_no = item.CODE,
                                    title = plu_info.TITLE
                                }
                            });
                        }
                        else
                        {
                            // 未找到商品信息，记录警告日志
                            _log.Warn($"【订单处理】未找到商品信息，订单号:{message.order_sn}，商品编码:{item.CODE}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    // 处理商品信息异常，记录错误日志
                    _log.Error($"【订单处理】处理商品信息异常，订单号:{message.order_sn}，商品编码:{item.CODE}，异常:{ex.Message}");
                }
            }

            // 设置订单商品信息
            request.trade_items = trade_items;

            // 处理门店信息
            var shop_info = await _shopAppService.getShopInfoById(orderItems[0].SHOPID);
            if (!shop_info.IsNull())
            {
                // 设置卖家（门店）信息
                request.seller = new sellerInfo()
                {
                    kdt_id = shop_info.source_no.ToLong(),  // 有赞门店ID
                    out_kdt_id = shop_info.store_no,        // 外部门店编号
                    shop_name = shop_info.store_name        // 门店名称
                };
            }
            else
            {
                _log.Error($"【订单处理】未找到门店信息，订单号:{message.order_sn}，门店ID:{orderItems[0].SHOPID}");
            }

            // 设置收件人信息（线下门店订单）
            request.receiver = new receiverInfo()
            {
                receiver_name = "门店顾客",
                logistics_type = "SELF_TAKE",
            };

            // 准备调用有赞接口
            var parameter = new YouzanParameter();
            parameter.url = $"/api/youzan.scrm.order.trade.out.create/1.0.0";
            parameter.body = request;

            // 调用有赞接口
            _log.Info($"【订单处理】开始调用有赞接口，订单号:{message.order_sn}");
            var response = await _youzanService.getYouzanData(parameter);

            // 记录接口调用日志
            WriteLog("同步POS销售单", parameter.url, parameter.body.ToJson(), response.ToJson());

            // 保存同步日志
            var syncLog = new syncLogsEntity
            {
                Type = "TRADE",
                ShopId = orderItems[0].SHOPID,
                DeviceId = orderItems[0].TERMINAL,
                SourceId = message.order_sn,
                SyncStatus = response.code.ToInt(),
                Body = response.ToJson()
            };
            await _orderRepository.saveSyncLogs(syncLog);

            // 检查接口调用结果
            if (!response.success)
            {
                _log.Error($"【订单处理】接口调用失败，订单号:{message.order_sn}，错误:{response.message}");
                //return;
            }
            else
            {
                _log.Info($"【订单处理】接口调用成功，订单号:{message.order_sn}，消息:{response.message}");
            }

            // 处理会员积分
            if (!orderItems[0].PHONE.IsEmpty())
            {
                // 计算积分（实际金额 * 0.01 / 积分系数）
                var cpts = orderItems.Where(x => x.TXNTYPE == 0 && x.SALESTYPE < 6)
                    .Sum(s => s.CPTS > 0 ? ((s.ORGAMT + s.ITEMDISC + s.TTLDISC) * 0.01) / s.CPTS : 0);
                if (cpts > 0)
                {
                    // 构建积分请求
                    var pointRequset = new customerPointRequest();
                    pointRequset.reason = $"门店消费增加积分-{orderItems[0].TXNNUM}";
                    pointRequset.source_kdt_id = request.seller.kdt_id;
                    pointRequset.points = (int)Math.Round(cpts);
                    pointRequset.biz_value = orderItems[0].TXNNUM;
                    pointRequset.user = new userInfo()
                    {
                        account_id = orderItems[0].PHONE,
                    };
                    // 同步积分到有赞系统
                    await syncPoint(pointRequset);
                }
            }

            // 记录处理完成日志
            _log.Info($"【订单处理】处理完成，订单号:{message.order_sn}");
        }
        catch (Exception ex)
        {
            // 记录异常日志并抛出异常
            _log.Error($"【订单处理】处理异常，订单号:{message.order_sn}，异常:{ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// 历史订单事件总线 - 不计算积分
    /// </summary>
    /// <param name="message">消息内容</param>
    /// <returns></returns>
    [CapSubscribe("orderHistoryEvent")]
    public async Task orderHistoryEvent(tradeMessage message)
    {
        var request = new orderTradeRequest();
        var orderItems = await _orderAppService.getshopTxnsList(message.order_sn);
        // 订单主体
        request.main_info = new orderTradeRequest.oderBaseInfo()
        {
            out_biz_no = message.order_sn,                    // 外部订单号
            create_time = orderItems[0].TXNTIME,              // 订单创建时间
            pay_time = orderItems[orderItems.Count - 1].TXNTIME > DateTime.Now 
                ? DateTime.Now 
                : orderItems[orderItems.Count - 1].TXNTIME, // 支付时间（如大于当前时间则用系统时间）
            order_status = "SUCCESS",                         // 订单状态：交易成功
            open_source = new OpenSource                      // 订单来源
            {
                order_status = "SUCCESS",                     // 订单状态：交易成功
                trade_mark = "POS",                          // 交易终端：POS机
                trade_channel = new TradeChannel             // 交易渠道
                {
                    common_channel = "POS"                    // 通用渠道：线下门店
                }
            }
        };

        // 计算订单价格信息
        // 计算原价（只计算普通销售类型的商品）
        var origin_price = orderItems.Where(x => x.TXNTYPE == 0 && x.SALESTYPE < 6).Sum(x => x.ORGAMT);
        // 计算实际销售价（原价 + 商品折扣 + 整单折扣）
        var current_price = orderItems.Where(x => x.TXNTYPE == 0 && x.SALESTYPE < 6).Sum(x => x.ORGAMT + x.ITEMDISC + x.TTLDISC);
        // 设置订单价格信息
        request.order_price = new orderPrice()
        {
            current_price = current_price,
            origin_price = origin_price,
            total_price = current_price,
            promotion_amount = origin_price - current_price
        };

        // 买家信息
        if (!orderItems[0].PHONE.IsEmpty())
        {
            var customer_info = await _memberAppService.getCustomerInfo(orderItems[0].PHONE);
            var yz_open_id = customer_info?.open_id;

            // 如果customer_info.open_id为空，用手机号码调用有赞API获取open_id
            if (string.IsNullOrEmpty(yz_open_id))
            {
                yz_open_id = await getYzOpenIdByPhone(orderItems[0].PHONE);
            }

            // 如果yz_open_id仍然为空，则不处理订单
            if (string.IsNullOrEmpty(yz_open_id))
            {
                _log.Warn($"【历史订单处理】无法获取有效的openId，跳过订单处理，订单号:{message.order_sn}，手机号:{orderItems[0].PHONE}");
                return;
            }

            // 买家信息
            request.buyer = new buyerInfo()
            {
                tel = orderItems[0].PHONE,
                yz_open_id = yz_open_id
            };
        }
        var trade_items = new List<tradeItems>();
        foreach (var item in orderItems.Where(x => x.TXNTYPE == 0 && x.SALESTYPE < 6))
        {
            var product_info = await _prodcutAppService.getProductSkuInfoWithBarcodeByChannel0(item.CODE);
            if (!product_info.IsNull())
            {
                trade_items.Add(new tradeItems()
                {
                    num = item.QTY,                                                     // 商品数量
                    origin_price = item.ORGAMT / item.QTY,                                // 商品原始价格（单位：分）
                    current_price = (item.ORGAMT + item.ITEMDISC + item.TTLDISC) / item.QTY, // 商品当前价格（单位：分）
                    current_total_amount = item.ORGAMT + item.ITEMDISC + item.TTLDISC,  // 商品订单实付金额（单位：分）
                    goods_info = new tradeItems.goodsInfo
                    {
                        item_no = product_info.bar_code,
                        title = product_info.sku_name,
                        sku_no = product_info.has_sku == 1 ? product_info.sku_sn : null
                    }
                });
            }
            else
            {
                var plu_info = await _prodcutAppService.getPluInfo(item.CODE);
                if (plu_info != null)
                {
                    trade_items.Add(new tradeItems()
                    {
                        num = item.QTY,                                                     // 商品数量
                        origin_price = item.ORGAMT / item.QTY,                                // 商品原始价格（单位：分）
                        current_price = (item.ORGAMT + item.ITEMDISC + item.TTLDISC) / item.QTY, // 商品当前价格（单位：分）
                        current_total_amount = item.ORGAMT + item.ITEMDISC + item.TTLDISC,  // 商品订单实付金额（单位：分）
                        goods_info = new tradeItems.goodsInfo()
                        {
                            item_no = item.CODE,
                            title = plu_info.TITLE
                        }
                    });
                }
            }
        }
        // 订单交易信息
        request.trade_items = trade_items;
        var shop_info = await _shopAppService.getShopInfoById(orderItems[0].SHOPID);
        if (!shop_info.IsNull())
        {
            request.seller = new sellerInfo()
            {
                kdt_id = shop_info.source_no.ToLong(),
                out_kdt_id = shop_info.store_no,
                shop_name = shop_info.store_name
            };
        }
        // 收件人
        request.receiver = new receiverInfo()
        {
            receiver_name = "线下门店",
            logistics_type = "NONE",
        };
        var parameter = new YouzanParameter();
        parameter.url = $"/api/youzan.scrm.order.trade.out.create/1.0.0";
        parameter.body = request;
        var response = await _youzanService.getYouzanData(parameter);
        WriteLog("同步历史POS销售单", parameter.url, parameter.body.ToJson(), response.ToJson());

        // 记录同步日志
        var syncLog = new syncLogsEntity
        {
            Type = "TRADE",
            ShopId = orderItems[0].SHOPID,
            DeviceId = orderItems[0].TERMINAL,
            SourceId = message.order_sn,
            SyncStatus = response.code.ToInt(),
            Body = response.ToJson()
        };
        await _orderRepository.saveSyncLogs(syncLog);
    }

    /// <summary>
    /// 同步积分
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    private async Task<ApiResult> syncPoint(customerPointRequest request)
    {
        var parameter = new YouzanParameter();
        parameter.url = $"/api/youzan.crm.customer.points.increase/4.0.0";
        var dict = new Dictionary<string, object>();
        dict.Add("params", request);
        parameter.body = dict;
        var response = await _youzanService.getYouzanData(parameter);
        WriteLog("新增客户积分", parameter.url, parameter.body.ToJson(), response.ToJson());
        if (response.success)
        {
            var result = response.data.ToString().ToObject<customerPointResponse>();
            return new ApiResult(StatusCode.Ok);
        }
        return new ApiResult(StatusCode.Fail, response.message);
    }


    /// <summary>
    /// 通过手机号获取有赞OpenID
    /// </summary>
    /// <param name="phone">手机号</param>
    /// <returns>有赞OpenID，如果获取失败返回空字符串</returns>
    private async Task<string> getYzOpenIdByPhone(string phone)
    {
        try
        {
            // 构建有赞API请求参数
            var parameter = new YouzanParameter();
            parameter.url = $"/api/youzan.scrm.customer.detail.get/1.0.1";
            var request = new customerDetailRequest();
            request.fields = "user_base"; // 只需要基础信息即可获取open_id
            request.account_info.account_type = 2; // 手机号类型
            request.account_info.account_id = phone;

            // 发送请求到有赞API
            parameter.body = request;
            var response = await _youzanService.getYouzanData(parameter);

            // 记录日志
            WriteLog("获取有赞OpenID", parameter.url, request.ToJson(), response.ToJson());

            // 处理API响应
            if (response.success && response.data != null)
            {
                var customer_info = response.data.ToString().ToObject<customerDetailResponse>();
                return customer_info?.yz_open_id ?? string.Empty;
            }
            else
            {
                _log.Warn($"【获取有赞OpenID】API调用失败，手机号:{phone}，错误:{response.message}");
                return string.Empty;
            }
        }
        catch (Exception ex)
        {
            _log.Error($"【获取有赞OpenID】处理异常，手机号:{phone}，异常:{ex.Message}");
            return string.Empty;
        }
    }

    /// <summary>
    /// 日志记录
    /// </summary>
    /// <param name="title">标题</param>
    /// <param name="pay_way">支付网关</param>
    /// <param name="requestData">请求数据</param>
    /// <param name="rawData">原始响应</param>
    private void WriteLog(string title, string pay_way, string requestData, string resultData)
    {
        var content = new StringBuilder();
        content.AppendLine($"请求地址:{pay_way}");
        content.AppendLine($"请求参数:{requestData}");
        content.AppendLine($"返回结果:{resultData}");
        _log.Set<LogContent>(p => p.Class = GetType().FullName)
            .Set<LogContent>(p => p.Caption = title)
            .Set<LogContent>(p => p.Content = content)
           .Info();
    }

}
