﻿using AppService.Dtos;
using BPM.Users;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.AspNetCore.Mvc.Filters;
using System.Collections.Generic;
using Web.Api.Common;

namespace Api.Filter
{
    /// <summary>
    /// 签名属性验证
    /// </summary>
    public class ValidateSignFilter : ActionFilterAttribute
    {
        /// <summary>
        /// 授权服务
        /// </summary>
        private readonly IApplicationService appService;

        /// <summary>
        /// 用户服务
        /// </summary>
        private readonly ICurrentUser current;
        /// <summary>
        /// 初始化服务
        /// </summary>
        /// <param name="_appService">应用服务</param>
        /// <param name="_current">用户上下文</param>
        /// <param name="userAppService">用户服务</param>
        public ValidateSignFilter(IApplicationService _appService, ICurrentUser _current)
        {
            appService = _appService;
            current = _current;
        }

        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2022-02-11
        /// Description:  请求拦截
        /// </summary>
        /// <param name="context">请求上下文</param>
        /// <returns></returns>
        public override async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
        {
            //开发者ID(AppID).
            string app_id = string.Empty;
            #region 判断是否加上了不需要拦截
            var action_name = ((ControllerActionDescriptor)context.ActionDescriptor).ActionName;  //当前请求action
            var noNeedCheck = false;
            if (context.ActionDescriptor is ControllerActionDescriptor controllerActionDescriptor)
            {
                noNeedCheck = controllerActionDescriptor.MethodInfo.GetCustomAttributes(inherit: true)
                  .Any(a => a.GetType().Equals(typeof(NoSignAttribute)));
            }
            if (noNeedCheck)
            {
                await next();
                return;
            }
            #endregion

            #region 校验 POST 参数集合
            var jsonData = (context.ActionArguments.Values).ToList();
            if (jsonData.IsEmpty())
            {
                context.Result = new ApiResult(StatusCode.Fail) { info = "POST参数非法" };
                return;
            }
            //请求参数转换维对象
            var dict = jsonData[0].ToJson().ToObject<Dictionary<string, object>>();
            if (action_name.Contains("access_token") && !dict.ContainsKey("app_id"))
            {
                app_id = dict["app_id"].ToString();
                context.Result = new ApiResult(StatusCode.Fail) { info = "参数错误，请检查请求参数app_id是否上传" };
                return;
            }
            if (dict.ContainsKey("app_id"))
                app_id = dict["app_id"].ToString();
            else
                app_id = current.FindClaimValue("application_id");
            #endregion

            #region 查找应用·验证应用是否合法/应用状态/签名密钥
            appAccountDto appAccount = await appService.GetAppAccountInfo(app_id);
            if (appAccount.IsNull())
            {
                context.Result = new ApiResult(StatusCode.Fail) { info = "应用编号不存在" };
                return;
            }
            if (appAccount.status == 0)
            {
                context.Result = new ApiResult(StatusCode.Fail) { info = "应用编号已注销" };
                return;
            }
            // 请求时间戳
            var timestamp1 = long.Parse(dict.GetValueOrDefault("time_stamp").ToString());
            // 服务器时间戳
            var timestamp2 = DataTimeHelper.DateTimeToTimeStamp(DateTime.Now);
            // 计算两个时间戳的差值
            long difference = timestamp2 - timestamp1;
            // 判断差值是否大于5分钟（300秒）
            if (difference > 300)
            {
                context.Result = new ApiResult(StatusCode.Fail) { info = "请检查服务器时间与客户端时间是否一致" };
                return;
            }
            //var aa = Domain.Shared.Encrypt.AppSignHelper.GetSign(dict, appAccount.app_secret);
            if (Domain.Shared.Encrypt.AppSignHelper.GetSign(dict, appAccount.app_secret) != dict.GetValueOrDefault("sign").ToString()) //验核签名信息是否合法
            {
                context.Result = new ApiResult(StatusCode.Fail) { info = "请检查签名参数和方法是否都符合签名算法要求" };
                return;
            }
            #endregion

            await next();
        }

        /// <summary>
        /// Action 执行后
        /// </summary>
        /// <param name="context"></param>
        public override Task OnResultExecutionAsync(ResultExecutingContext context, ResultExecutionDelegate next)
        {
            return base.OnResultExecutionAsync(context, next);
        }
    }

    /// <summary>
    /// 不需要登陆的地方加个这个空的拦截器
    /// </summary>
    public class NoSignAttribute : ActionFilterAttribute { }
}