﻿using Api.Filter;
using BPM.AspNetCore;
using BPM.AspNetCore.Mvc.Filters;
using BPM.Core.Modularity;
using BPM.Domain.Entities.Events;
using BPM.Security.Claims;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Mvc.ApplicationModels;
using Microsoft.AspNetCore.Mvc.Authorization;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System.ComponentModel;
using System.Text;
using Web.Api.Configs;

namespace BPM.Api.Modules
{
    /// <summary>
    /// 应用程序模块
    /// </summary>
    [Description("应用程序模块")]
    [DependsOn(typeof(AspNetCoreModule))]
    public class AppModule : AspNetCoreBPMModule
    {
        /// <summary>
        /// 模块级别。级别越小越先启动
        /// </summary>
        public override ModuleLevel Level => ModuleLevel.Application;

        /// <summary>
        /// 添加服务。将模块服务添加到依赖注入服务容器中
        /// </summary>
        /// <param name="services">服务集合</param>
        public override IServiceCollection AddServices(IServiceCollection services)
        {
            BPMClaimTypes.UserId = IdentityModel.JwtClaimTypes.Subject;
            BPMClaimTypes.UserName = IdentityModel.JwtClaimTypes.Name;
            // 注册Mvc
            services.AddMvc(options =>
                {
                    //异常捕获
                    options.Filters.Add<ExceptionHandlerAttribute>();
                    //验证实体过滤器
                    options.Filters.Add<ValidationModelAttribute>();
                    //sign签名验证
                    options.Filters.Add<ValidateSignFilter>();
                    // 全局添加授权
                    options.Conventions.Add(new AuthorizeControllerModelConvention());

                })
                .AddNewtonsoftJson(options =>
                {
                    options.SerializerSettings.DateFormatHandling = DateFormatHandling.MicrosoftDateFormat;
                    options.SerializerSettings.DateFormatString = "yyyy-MM-dd HH:mm:ss";
                    options.SerializerSettings.ContractResolver = new DefaultContractResolver();
                })
                .AddControllersAsServices();
            //关闭自动验证 走过滤器进行验证
            services.Configure<ApiBehaviorOptions>(options => options.SuppressModelStateInvalidFilter = true);
            //跨域
            ConfigureCors(services);
            //注入领域事件
            services.AddDomainEventDispatcher();
            //注入HttpClient
            services.AddHttpClient();
            //注册接口基础配置
            var configuration = services.GetConfiguration();
            var options = configuration.GetSection(nameof(ServiceOptions)).Get<ServiceOptions>();
            services.TryAddSingleton<IServiceConfigProvider>(new ServiceConfigProvider(options));

            return services;
        }

        /// <summary>
        /// 应用AspNetCore的服务业务
        /// </summary>
        /// <param name="app">应用程序构建器</param>
        public override void UseModule(IApplicationBuilder app)
        {
            Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
            app.UseBPMExceptionHandling();
            //开启Cors跨域访问
            app.UseCors("AllowAllOrigins");
            // 初始化Http上下文访问器
            BPM.Helpers.Web.HttpContextAccessor = app.ApplicationServices.GetService<IHttpContextAccessor>();
            //初始化HttpHosting
            BPM.Helpers.Web.Environment = app.ApplicationServices.GetService<Microsoft.AspNetCore.Hosting.IWebHostEnvironment>();

            app.UseAuthentication();

            app.UseRouting();

            app.UseAuthorization();

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
            });

            Enabled = true;

        }

        /// <summary>
        /// 配置跨域服务
        /// </summary>
        /// <param name="services"></param>
        private void ConfigureCors(IServiceCollection services)
        {
            var configuration = services.GetConfiguration();
            var crossUrl = configuration.GetConnectionString("CrossUrl");
            if (!crossUrl.IsNull())
            {
                string[] crossUrls = crossUrl.Split(",");
                services.AddCors(options =>
                {
                    options.AddPolicy("AllowAllOrigins", builder =>
                    {
                        builder.WithOrigins(crossUrls).SetIsOriginAllowedToAllowWildcardSubdomains()
                            .AllowAnyHeader().AllowAnyMethod().AllowCredentials();
                    });
                });
            }
        }

        /// <summary>
        /// 授权控制器模型转换器
        /// </summary>
        internal class AuthorizeControllerModelConvention : IControllerModelConvention
        {
            /// <summary>
            /// 实现Apply
            /// </summary>
            public void Apply(ControllerModel controller) => controller.Filters.Add(new AuthorizeFilter("jwt"));
        }
    }
}
