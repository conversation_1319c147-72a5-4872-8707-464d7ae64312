﻿using BPM.AspNetCore;
using BPM.Core.Modularity;
using BPM.Events.Cap;
using Infrastructure.Cap;
using Infrastructure.UnitOfWorks.SqlServer;
using Microsoft.Extensions.DependencyInjection;
using System.ComponentModel;
using Web.Api.EventHandlers.Youzan;
using System;

namespace BPM.Api.Modules
{
    /// <summary>
    /// CAP模块
    /// </summary>
    [Description("CAP模块")]
    [DependsOn(typeof(AspNetCoreModule))]
    public class CapModule : AspNetCoreBPMModule
    {
        /// <summary>
        /// 模块级别。级别越小越先启动
        /// </summary>
        public override ModuleLevel Level => ModuleLevel.Application;

        /// <summary>
        /// 模块启动顺序。模块启动的顺序先按级别启动，同一级别内部再按此顺序启动，
        /// 级别默认为0，表示无依赖，需要在同级别有依赖顺序的时候，再重写为>0的顺序值
        /// </summary>
        public override int Order => 1;

        /// <summary>
        /// 添加服务。将模块服务添加到依赖注入服务容器中
        /// </summary>
        /// <param name="services">服务集合</param>
        public override IServiceCollection AddServices(IServiceCollection services)
        {
            //获取配置服务
            var configuration = services.GetConfiguration();
            var options = configuration.GetSection(nameof(RabbitOptions)).Get<RabbitOptions>();
            
            // 添加事件总线服务
            services.AddCapEventBus(o =>
            {
                //配置Cap的本地消息记录库，用于服务端保存Published消息记录表；客户端保存Received消息记录表
                o.UseEntityFramework<BaseUnitOfWork>();
                
                // 设置消费者组 - 使用正确的配置方式
                o.GroupNamePrefix = "ipos.order.group";
                
                // 设置消费者并发数
                o.ConsumerThreadCount = 5;
                
                // 设置处理成功的数据在数据库中保存的时间（秒），为保证系统性能，数据会定期清理
                o.SucceedMessageExpiredAfter = 24 * 3600;
                
                // 设置失败重试策略
                o.FailedRetryCount = 5;
                o.FailedRetryInterval = 60;
                
                // 版本号
                o.Version = "v1";
                
                o.UseRabbitMQ(x =>
                {
                    x.UserName = options.UserName;
                    x.Password = options.Password;
                    x.HostName = options.HostName;
                    x.Port = options.Port;
                    x.VirtualHost = "/";
                    
                    // 添加连接恢复和重试策略
                    x.ConnectionFactoryOptions = opt => 
                    {
                        opt.AutomaticRecoveryEnabled = true;
                        opt.NetworkRecoveryInterval = TimeSpan.FromSeconds(10);
                        opt.RequestedHeartbeat = TimeSpan.FromSeconds(30);
                    };
                });
            });
            
            // 确保在CAP配置后注册消费者
            LoadEvent(services);

            return services;
        }

        /// <summary>
        /// 加载事件
        /// </summary>
        /// <param name="services">服务集合</param>
        protected void LoadEvent(IServiceCollection services)
        {
            // 先注册有赞订单处理事件处理器
            services.AddTransient<orderTreadeEventHandler>();
            // 再注册订单事件总线
            services.AddTransient<EventHandlers.OrderEventHandler>();
        }
    }
}
