﻿using BPM.AspNetCore;
using BPM.Core.Modularity;
using BPM.Extras.Card.Options;
using BPM.Extras.Card.Services;
using Microsoft.Extensions.DependencyInjection;
using System.ComponentModel;

namespace BPM.Api.Modules
{
    /// <summary>
    /// 储值卡模块
    /// </summary>
    [Description("储值卡模块")]
    [DependsOn(typeof(AspNetCoreModule))]
    public class CardModule : AspNetCoreBPMModule
    {
        /// <summary>
        /// 模块级别。级别越小越先启动
        /// </summary>
        public override ModuleLevel Level => ModuleLevel.Framework;

        /// <summary>
        /// 模块启动顺序。模块启动的顺序先按级别启动，同一级别内部再按此顺序启动，
        /// 级别默认为0，表示无依赖，需要在同级别有依赖顺序的时候，再重写为>0的顺序值
        /// </summary>
        public override int Order => 1;

        /// <summary>
        /// 添加服务。将模块服务添加到依赖注入服务容器中
        /// </summary>
        /// <param name="services">服务集合</param>
        public override IServiceCollection AddServices(IServiceCollection services)
        {
            ConfigureCardOptions(services);
            services.AddTransient<ICardService, CardService>();
            return services;
        }

        /// <summary>
        /// 添加有赞配置
        /// </summary>
        /// <param name="services"></param>
        private static void ConfigureCardOptions(IServiceCollection services)
        {
            var configuration = services.GetConfiguration();
            var options = configuration.GetSection(nameof(CardOptions)).Get<CardOptions>();
            // 配置验证
            services.AddOptions<CardOptions>().Configure<IServiceProvider>((option, service) =>
            {
                // 通过注入的服务执行相应的逻辑
                option.app_id = options.app_id;
                option.app_secret= options.app_secret;
                option.authorize_type= options.authorize_type;
                option.service_url= options.service_url;
                option.sign_key= options.sign_key;
            });
        }
    }
}
