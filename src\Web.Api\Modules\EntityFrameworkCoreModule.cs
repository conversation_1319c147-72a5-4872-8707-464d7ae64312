﻿using BPM.Core.Modularity;
using BPM.Data;
using BPM.Data.Enums;
using BPM.Datas.Dapper;
using BPM.Datas.EntityFramework.SqlServer;
using Infrastructure.UnitOfWorks;
using Infrastructure.UnitOfWorks.SqlServer;
using Microsoft.Extensions.DependencyInjection;
using System.ComponentModel;
using BPM.Datas.FreeSQL.Extensions;
using FreeSql;

namespace BPM.Api.Modules
{
    /// <summary>
    /// EntityFrameworkCore模块
    /// </summary>
    [Description("EntityFrameworkCore模块")]
    public class EntityFrameworkCoreModule : BPMModule
    {
        /// <summary>
        /// 模块级别。级别越小越先启动
        /// </summary>
        public override ModuleLevel Level => ModuleLevel.Framework;

        /// <summary>
        /// 模块启动顺序。模块启动的顺序先按级别启动，同一级别内部再按此顺序启动，
        /// 级别默认为0，表示无依赖，需要在同级别有依赖顺序的时候，再重写为>0的顺序值
        /// </summary>
        public override int Order => 1;

        /// <summary>
        /// 添加服务。将模块服务添加到依赖注入服务容器中
        /// </summary>
        /// <param name="services">服务集合</param>
        public override IServiceCollection AddServices(IServiceCollection services)
        {
            var configuration = services.GetConfiguration();
            var connectionStr = configuration.GetConnectionString("DefaultConnection");
            // 注册工作单元
            services.AddSqlServerUnitOfWork<IBaseUnitOfWork, BaseUnitOfWork>(connectionStr);
            // 注册SqlQuery 默认注册基础在服务
            services.AddSqlQuery<BaseUnitOfWork, BaseUnitOfWork>(options =>
                {
                    options.DatabaseType = DatabaseType.SqlServer;
                    options.IsClearAfterExecution = true;
                    options.LogLevel = DataLogLevel.Sql;
                });
            // 注册SqlExecutor
            services.AddSqlExecutor();
            // 注册FreeSql 限制 新增、删除、修改
            services.AddFreeSql(options =>
            {
                options.databaseType = DataType.SqlServer;
                options.connection = connectionStr;
            });


            return services;
        }
    }
}
