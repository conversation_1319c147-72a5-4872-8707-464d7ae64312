﻿using BPM.AspNetCore;
using BPM.AutoMapper;
using BPM.Core.Modularity;
using Microsoft.Extensions.DependencyInjection;
using System.ComponentModel;

namespace BPM.Api.Modules
{
    /// <summary>
    /// AutoMapper 模块
    /// </summary>
    [Description("AutoMapper 模块")]
    [DependsOn(typeof(AspNetCoreModule))]
    public class MapperModule : AspNetCoreBPMModule
    {
        /// <summary>
        /// 模块级别。级别越小越先启动
        /// </summary>
        public override ModuleLevel Level => ModuleLevel.Framework;

        /// <summary>
        /// 添加服务。将模块服务添加到依赖注入服务容器中
        /// </summary>
        /// <param name="services">服务集合</param>
        public override IServiceCollection AddServices(IServiceCollection services)
        {
            services.AddAutoMapper();
            return services;
        }
    }
}
