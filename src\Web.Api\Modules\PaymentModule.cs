﻿using BPM.AspNetCore;
using BPM.Core.Modularity;
using Microsoft.Extensions.DependencyInjection;
using System.ComponentModel;

namespace BPM.Api.Modules
{
    /// <summary>
    /// 应用程序模块
    /// </summary>
    [Description("支付模块")]
    [DependsOn(typeof(AspNetCoreModule))]
    public class PaymentModule : AspNetCoreBPMModule
    {
        /// <summary>
        /// 模块级别。级别越小越先启动
        /// </summary>
        public override ModuleLevel Level => ModuleLevel.Application;

        /// <summary>
        /// 添加服务。将模块服务添加到依赖注入服务容器中
        /// </summary>
        /// <param name="services">服务集合</param>
        public override IServiceCollection AddServices(IServiceCollection services)
        {
            //注册微信支付服务
            services.AddWeChatPay();
            //注册支付宝服务
            services.AddAlipay();
            return services;
        }

    }
}
