﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <UserSecretsId>a1ad56ee-c2b9-4367-be33-f50f31e034a3</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AspectCore.Extensions.AspectScope" Version="2.4.0" />
    <PackageReference Include="AspectCore.Extensions.Hosting" Version="2.4.0" />
    <PackageReference Include="AspectCore.Extensions.Reflection" Version="2.4.0" />
    <PackageReference Include="Essensoft.Paylink.Alipay" Version="4.1.4" />
    <PackageReference Include="Essensoft.Paylink.Security" Version="4.1.4" />
    <PackageReference Include="Essensoft.Paylink.WeChatPay" Version="4.1.4" />
    <PackageReference Include="IdentityModel" Version="5.1.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="6.0.21" NoWarn="NU1605" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="6.0.21" NoWarn="NU1605" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="6.0.21" />
    <PackageReference Include="Microsoft.Extensions.DependencyModel" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.PlatformAbstractions" Version="1.1.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="5.6.3" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.2.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\AppService\AppService.csproj" />
    <ProjectReference Include="..\BPM.Extras.Card\BPM.Extras.Card.csproj" />
    <ProjectReference Include="..\BPM.Extras.Youzan\BPM.Extras.Youzan.csproj" />
    <ProjectReference Include="..\EventHandlers\EventHandlers.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="BPM.AspNetCore">
      <HintPath>..\..\dlls\BPM.AspNetCore.dll</HintPath>
    </Reference>
    <Reference Include="BPM.AutoMapper">
      <HintPath>..\..\dlls\BPM.AutoMapper.dll</HintPath>
    </Reference>
    <Reference Include="BPM.Caching.CSRedis">
      <HintPath>..\..\dlls\BPM.Caching.CSRedis.dll</HintPath>
    </Reference>
    <Reference Include="BPM.Core">
      <HintPath>..\..\dlls\BPM.Core.dll</HintPath>
    </Reference>
    <Reference Include="BPM.Data">
      <HintPath>..\..\dlls\BPM.Data.dll</HintPath>
    </Reference>
    <Reference Include="BPM.Data.Sql">
      <HintPath>..\..\dlls\BPM.Data.Sql.dll</HintPath>
    </Reference>
    <Reference Include="BPM.Datas.Dapper">
      <HintPath>..\..\dlls\BPM.Datas.Dapper.dll</HintPath>
    </Reference>
    <Reference Include="BPM.Datas.EntityFramework">
      <HintPath>..\..\dlls\BPM.Datas.EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="BPM.Datas.EntityFramework.SqlServer">
      <HintPath>..\..\dlls\BPM.Datas.EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="BPM.Datas.FreeSQL">
      <HintPath>..\..\dlls\BPM.Datas.FreeSQL.dll</HintPath>
    </Reference>
    <Reference Include="BPM.Ddd.Application">
      <HintPath>..\..\dlls\BPM.Ddd.Application.dll</HintPath>
    </Reference>
    <Reference Include="BPM.Ddd.Application.Contracts">
      <HintPath>..\..\dlls\BPM.Ddd.Application.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="BPM.Ddd.Domain">
      <HintPath>..\..\dlls\BPM.Ddd.Domain.dll</HintPath>
    </Reference>
    <Reference Include="BPM.Events">
      <HintPath>..\..\dlls\BPM.Events.dll</HintPath>
    </Reference>
    <Reference Include="BPM.Logs">
      <HintPath>..\..\dlls\BPM.Logs.dll</HintPath>
    </Reference>
    <Reference Include="BPM.Logs.NLog">
      <HintPath>..\..\dlls\BPM.Logs.NLog.dll</HintPath>
    </Reference>
    <Reference Include="BPM.Mapper">
      <HintPath>..\..\dlls\BPM.Mapper.dll</HintPath>
    </Reference>
    <Reference Include="BPM.Permissions">
      <HintPath>..\..\dlls\BPM.Permissions.dll</HintPath>
    </Reference>
    <Reference Include="BPM.Security">
      <HintPath>..\..\dlls\BPM.Security.dll</HintPath>
    </Reference>
    <Reference Include="BPM.Utils">
      <HintPath>..\..\dlls\BPM.Utils.dll</HintPath>
    </Reference>
    <Reference Include="BPM.Utils.Drawing">
      <HintPath>..\..\dlls\BPM.Utils.Drawing.dll</HintPath>
    </Reference>
    <Reference Include="BPM.Utils.Http">
      <HintPath>..\..\dlls\BPM.Utils.Http.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <None Update="Dockerfile">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ProjectExtensions><VisualStudio><UserProperties appsettings_1json__JsonSchema="" properties_4launchsettings_1json__JsonSchema="" /></VisualStudio></ProjectExtensions>

</Project>
