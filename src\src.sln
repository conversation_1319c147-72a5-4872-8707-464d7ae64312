Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.5.2.0
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AppService", "AppService\AppService.csproj", "{E900B01A-D6E9-7BB8-8A35-02E3ECA6E2BD}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BPM.Extras.Card", "BPM.Extras.Card\BPM.Extras.Card.csproj", "{7EE3D431-4FD4-64A7-03DE-03D038083CF0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BPM.Extras.Youzan", "BPM.Extras.Youzan\BPM.Extras.Youzan.csproj", "{719231D6-3FFF-ADBF-671E-D24880D4F69A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Domain", "Domain\Domain.csproj", "{E528BBC3-1C26-8C35-F682-23718816ADCD}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Domain.Shared", "Domain.Shared\Domain.Shared.csproj", "{F0F79B9C-1DD4-B267-A0D2-A493ECC10D5B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "EventHandlers", "EventHandlers\EventHandlers.csproj", "{20C02C3A-5256-8475-2D54-EEEF9674630C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Infrastructure", "Infrastructure\Infrastructure.csproj", "{5872AEB3-BAA3-FE31-B0EB-0FB6DBEDCF83}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Web.Api", "Web.Api\Web.Api.csproj", "{EF380AE5-9296-86D2-850D-67277842E67F}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{E900B01A-D6E9-7BB8-8A35-02E3ECA6E2BD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E900B01A-D6E9-7BB8-8A35-02E3ECA6E2BD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E900B01A-D6E9-7BB8-8A35-02E3ECA6E2BD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E900B01A-D6E9-7BB8-8A35-02E3ECA6E2BD}.Release|Any CPU.Build.0 = Release|Any CPU
		{7EE3D431-4FD4-64A7-03DE-03D038083CF0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7EE3D431-4FD4-64A7-03DE-03D038083CF0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7EE3D431-4FD4-64A7-03DE-03D038083CF0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7EE3D431-4FD4-64A7-03DE-03D038083CF0}.Release|Any CPU.Build.0 = Release|Any CPU
		{719231D6-3FFF-ADBF-671E-D24880D4F69A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{719231D6-3FFF-ADBF-671E-D24880D4F69A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{719231D6-3FFF-ADBF-671E-D24880D4F69A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{719231D6-3FFF-ADBF-671E-D24880D4F69A}.Release|Any CPU.Build.0 = Release|Any CPU
		{E528BBC3-1C26-8C35-F682-23718816ADCD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E528BBC3-1C26-8C35-F682-23718816ADCD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E528BBC3-1C26-8C35-F682-23718816ADCD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E528BBC3-1C26-8C35-F682-23718816ADCD}.Release|Any CPU.Build.0 = Release|Any CPU
		{F0F79B9C-1DD4-B267-A0D2-A493ECC10D5B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F0F79B9C-1DD4-B267-A0D2-A493ECC10D5B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F0F79B9C-1DD4-B267-A0D2-A493ECC10D5B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F0F79B9C-1DD4-B267-A0D2-A493ECC10D5B}.Release|Any CPU.Build.0 = Release|Any CPU
		{20C02C3A-5256-8475-2D54-EEEF9674630C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{20C02C3A-5256-8475-2D54-EEEF9674630C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{20C02C3A-5256-8475-2D54-EEEF9674630C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{20C02C3A-5256-8475-2D54-EEEF9674630C}.Release|Any CPU.Build.0 = Release|Any CPU
		{5872AEB3-BAA3-FE31-B0EB-0FB6DBEDCF83}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5872AEB3-BAA3-FE31-B0EB-0FB6DBEDCF83}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5872AEB3-BAA3-FE31-B0EB-0FB6DBEDCF83}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5872AEB3-BAA3-FE31-B0EB-0FB6DBEDCF83}.Release|Any CPU.Build.0 = Release|Any CPU
		{EF380AE5-9296-86D2-850D-67277842E67F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EF380AE5-9296-86D2-850D-67277842E67F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EF380AE5-9296-86D2-850D-67277842E67F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EF380AE5-9296-86D2-850D-67277842E67F}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {5DA06510-B203-4596-9E23-FC1AAE076BCF}
	EndGlobalSection
EndGlobal
