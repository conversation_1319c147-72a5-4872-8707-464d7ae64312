@echo off
chcp 65001 >nul
echo ========================================
echo  快速发布脚本
echo ========================================

:: 快速发布配置
set PROJECT_NAME=Web.Api\Web.Api.csproj
set PUBLISH_DIR=publish
set CONFIG_ENV=Release

:: 获取时间戳
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "timestamp=%dt:~0,4%%dt:~4,2%%dt:~6,2%_%dt:~8,2%%dt:~10,2%%dt:~12,2%"

echo [%time%] 开始快速发布...

:: 清理
if exist "%PUBLISH_DIR%" rmdir /s /q "%PUBLISH_DIR%"

:: 发布
echo 正在发布应用程序...
dotnet publish %PROJECT_NAME% ^
    --configuration %CONFIG_ENV% ^
    --output %PUBLISH_DIR% ^
    --verbosity minimal ^
    --self-contained false

if errorlevel 1 (
    echo 发布失败
    pause
    exit /b 1
)

echo "%PUBLISH_DIR%"
rem explorer "%PUBLISH_DIR%"
 
pause